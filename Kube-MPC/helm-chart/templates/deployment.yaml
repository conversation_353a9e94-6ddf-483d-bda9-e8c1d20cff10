apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "kubernetes-mcp-proxy.fullname" . }}
  labels:
    {{- include "kubernetes-mcp-proxy.labels" . | nindent 4 }}
spec:
  replicas: 1
  selector:
    matchLabels:
      {{- include "kubernetes-mcp-proxy.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "kubernetes-mcp-proxy.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "kubernetes-mcp-proxy.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: proxy
          securityContext:
            {{- toYaml .Values.proxy.securityContext | nindent 12 }}
          image: "{{ .Values.proxy.image.repository }}:{{ .Values.proxy.image.tag }}"
          imagePullPolicy: {{ .Values.proxy.image.pullPolicy }}
          env:
            - name: SAAS_TUNNEL_URL
              value: {{ .Values.proxy.saasUrl | quote }}
            - name: CLUSTER_TOKEN
              valueFrom:
                secretKeyRef:
                  name: {{ include "kubernetes-mcp-proxy.fullname" . }}-token
                  key: token
            - name: CLUSTER_ID
              valueFrom:
                configMapKeyRef:
                  name: {{ include "kubernetes-mcp-proxy.fullname" . }}-config
                  key: cluster-id
            - name: CLUSTER_NAME
              valueFrom:
                configMapKeyRef:
                  name: {{ include "kubernetes-mcp-proxy.fullname" . }}-config
                  key: cluster-name
            - name: RECONNECT_INTERVAL
              value: {{ .Values.proxy.reconnectInterval | quote }}
            - name: PING_INTERVAL
              value: {{ .Values.proxy.pingInterval | quote }}
          resources:
            {{- toYaml .Values.proxy.resources | nindent 12 }}
          volumeMounts:
            - name: tmp
              mountPath: /tmp
          livenessProbe:
            exec:
              command:
                - python
                - -c
                - "import sys; sys.exit(0)"
            initialDelaySeconds: 30
            periodSeconds: 30
          readinessProbe:
            exec:
              command:
                - python
                - -c
                - "import sys; sys.exit(0)"
            initialDelaySeconds: 5
            periodSeconds: 10
      volumes:
        - name: tmp
          emptyDir: {}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
