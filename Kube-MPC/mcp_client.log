2025-07-31 23:32:41,032 - <PERSON><PERSON><PERSON> - ERROR - <PERSON><PERSON><PERSON> connecting to Kubernetes MCP server: [Errno 2] No such file or directory: 'uvx'
2025-07-31 23:32:41,037 - MC<PERSON>lient - INFO - Disconnected from MCP server
2025-07-31 23:33:38,620 - MC<PERSON><PERSON> - INFO - Connected to Kubernetes MCP server (cluster: default)
2025-07-31 23:33:38,621 - MCPClient - INFO - Available Kubernetes tools: ['configuration_view', 'events_list', 'helm_install', 'helm_list', 'helm_uninstall', 'namespaces_list', 'pods_delete', 'pods_exec', 'pods_get', 'pods_list', 'pods_list_in_namespace', 'pods_log', 'pods_run', 'pods_top', 'resources_create_or_update', 'resources_delete', 'resources_get', 'resources_list']
