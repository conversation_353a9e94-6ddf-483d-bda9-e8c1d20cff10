import asyncio
import json
import uuid
import logging
from typing import Dict, Optional
from datetime import datetime, timedelta
import jwt
from fastapi import WebSocket, HTTPException
import base64

logger = logging.getLogger(__name__)


class TunnelSession:
    """Represents an active tunnel session from a customer cluster"""

    def __init__(self, cluster_id: str, websocket: WebSocket, auth_token: str):
        self.cluster_id = cluster_id
        self.websocket = websocket
        self.auth_token = auth_token
        self.session_id = str(uuid.uuid4())
        self.created_at = datetime.now()
        self.last_ping = datetime.now()
        self.is_active = True
        self.pending_requests: Dict[str, asyncio.Future] = {}

    async def send_request(
        self,
        request_id: str,
        method: str,
        path: str,
        headers: Dict,
        body: Optional[bytes] = None,
    ):
        """Send a Kubernetes API request through the tunnel"""
        try:
            message = {
                "type": "k8s_request",
                "request_id": request_id,
                "method": method,
                "path": path,
                "headers": headers,
                "body": base64.b64encode(body).decode() if body else None,
            }

            await self.websocket.send_text(json.dumps(message))
            logger.debug(f"Sent request {request_id} to cluster {self.cluster_id}")

        except Exception as e:
            logger.error(f"Error sending request through tunnel: {e}")
            raise

    async def handle_response(self, response_data: Dict):
        """Handle a response from the tunnel"""
        try:
            request_id = response_data.get("request_id")
            if request_id and request_id in self.pending_requests:
                future = self.pending_requests.pop(request_id)
                if not future.done():
                    future.set_result(response_data)

        except Exception as e:
            logger.error(f"Error handling tunnel response: {e}")

    async def ping(self):
        """Send ping to keep connection alive"""
        try:
            message = {"type": "ping", "timestamp": datetime.now().isoformat()}
            await self.websocket.send_text(json.dumps(message))
            self.last_ping = datetime.now()

        except Exception as e:
            logger.error(f"Error sending ping: {e}")
            self.is_active = False

    def is_healthy(self) -> bool:
        """Check if the tunnel session is healthy"""
        return self.is_active and datetime.now() - self.last_ping < timedelta(minutes=5)


class TunnelManager:
    """Manages all active tunnel sessions"""

    def __init__(self, secret_key: str):
        self.secret_key = secret_key
        self.sessions: Dict[str, TunnelSession] = {}
        self.cluster_tokens: Dict[str, str] = {}  # cluster_id -> auth_token
        self._cleanup_task = None

    def start_cleanup_task(self):
        """Start background task to cleanup inactive sessions"""
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._cleanup_inactive_sessions())

    async def _cleanup_inactive_sessions(self):
        """Background task to cleanup inactive tunnel sessions"""
        while True:
            try:
                inactive_sessions = [
                    cluster_id
                    for cluster_id, session in self.sessions.items()
                    if not session.is_healthy()
                ]

                for cluster_id in inactive_sessions:
                    logger.info(f"Cleaning up inactive tunnel session: {cluster_id}")
                    await self.disconnect_cluster(cluster_id)

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                logger.error(f"Error in cleanup task: {e}")
                await asyncio.sleep(60)

    def generate_cluster_token(self, cluster_id: str, cluster_name: str) -> str:
        """Generate a JWT token for cluster authentication"""
        payload = {
            "cluster_id": cluster_id,
            "cluster_name": cluster_name,
            "issued_at": datetime.now().isoformat(),
            "expires_at": (datetime.now() + timedelta(days=365)).isoformat(),
        }

        token = jwt.encode(payload, self.secret_key, algorithm="HS256")
        self.cluster_tokens[cluster_id] = token
        return token

    def verify_cluster_token(self, token: str) -> Optional[Dict]:
        """Verify and decode a cluster authentication token"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=["HS256"])

            # Check expiration
            expires_at = datetime.fromisoformat(payload["expires_at"])
            if datetime.now() > expires_at:
                return None

            return payload

        except jwt.InvalidTokenError:
            return None

    async def connect_cluster(
        self, cluster_id: str, websocket: WebSocket, auth_token: str
    ) -> bool:
        """Connect a new cluster tunnel session"""
        try:
            # Verify authentication token
            token_payload = self.verify_cluster_token(auth_token)
            if not token_payload or token_payload["cluster_id"] != cluster_id:
                logger.warning(f"Invalid authentication token for cluster {cluster_id}")
                return False

            # Disconnect existing session if any
            if cluster_id in self.sessions:
                await self.disconnect_cluster(cluster_id)

            # Create new session
            session = TunnelSession(cluster_id, websocket, auth_token)
            self.sessions[cluster_id] = session

            logger.info(f"Connected tunnel session for cluster {cluster_id}")
            return True

        except Exception as e:
            logger.error(f"Error connecting cluster {cluster_id}: {e}")
            return False

    async def disconnect_cluster(self, cluster_id: str):
        """Disconnect a cluster tunnel session"""
        try:
            if cluster_id in self.sessions:
                session = self.sessions[cluster_id]
                session.is_active = False

                # Cancel pending requests
                for future in session.pending_requests.values():
                    if not future.done():
                        future.cancel()

                # Close WebSocket if still open
                try:
                    await session.websocket.close()
                except Exception:
                    pass

                del self.sessions[cluster_id]
                logger.info(f"Disconnected tunnel session for cluster {cluster_id}")

        except Exception as e:
            logger.error(f"Error disconnecting cluster {cluster_id}: {e}")

    def get_session(self, cluster_id: str) -> Optional[TunnelSession]:
        """Get an active tunnel session for a cluster"""
        session = self.sessions.get(cluster_id)
        if session and session.is_healthy():
            return session
        return None

    async def proxy_kubernetes_request(
        self,
        cluster_id: str,
        method: str,
        path: str,
        headers: Dict,
        body: Optional[bytes] = None,
    ) -> Dict:
        """Proxy a Kubernetes API request through the tunnel"""
        session = self.get_session(cluster_id)
        if not session:
            raise HTTPException(
                status_code=503, detail=f"No active tunnel for cluster {cluster_id}"
            )

        request_id = str(uuid.uuid4())

        # Create future for response
        response_future: asyncio.Future = asyncio.Future()
        session.pending_requests[request_id] = response_future

        try:
            # Send request through tunnel
            await session.send_request(request_id, method, path, headers, body)

            # Wait for response with timeout
            response = await asyncio.wait_for(response_future, timeout=30.0)

            return {
                "status_code": response.get("status_code", 200),
                "headers": response.get("headers", {}),
                "body": base64.b64decode(response.get("body", ""))
                if response.get("body")
                else b"",
            }

        except asyncio.TimeoutError:
            # Clean up pending request
            session.pending_requests.pop(request_id, None)
            raise HTTPException(status_code=504, detail="Request timeout")

        except Exception as e:
            # Clean up pending request
            session.pending_requests.pop(request_id, None)
            logger.error(f"Error proxying request: {e}")
            raise HTTPException(status_code=500, detail=f"Proxy error: {str(e)}")

    def get_cluster_status(self) -> Dict[str, Dict]:
        """Get status of all connected clusters"""
        status = {}
        for cluster_id, session in self.sessions.items():
            status[cluster_id] = {
                "session_id": session.session_id,
                "connected_at": session.created_at.isoformat(),
                "last_ping": session.last_ping.isoformat(),
                "is_healthy": session.is_healthy(),
                "pending_requests": len(session.pending_requests),
            }
        return status


# Global tunnel manager instance
tunnel_manager: Optional[TunnelManager] = None


def get_tunnel_manager() -> TunnelManager:
    """Get the global tunnel manager instance"""
    global tunnel_manager
    if tunnel_manager is None:
        raise RuntimeError("Tunnel manager not initialized")
    return tunnel_manager


def initialize_tunnel_manager(secret_key: str):
    """Initialize the global tunnel manager"""
    global tunnel_manager
    tunnel_manager = TunnelManager(secret_key)
    tunnel_manager.start_cleanup_task()
    logger.info("Tunnel manager initialized")
