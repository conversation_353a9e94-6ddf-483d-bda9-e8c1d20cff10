from typing import Optional
from contextlib import AsyncExitStack
import traceback
import json
import os
from datetime import datetime

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from anthropic import Anthropic

from utils.logger import logger
from cluster_manager import ClusterManager


class MCPClient:
    def __init__(self):
        # Initialize session and client objects
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.llm = Anthropic()
        self.tools = []
        self.conversation_history = []  # Persistent conversation history
        self.messages = []  # Current message batch for LLM
        self.last_tool_results = []
        self.logger = logger
        self.cluster_manager = ClusterManager()  # Initialize cluster manager

    # connect to the MCP server
    async def connect_to_server(self, server_script_path: str):
        try:
            is_python = server_script_path.endswith(".py")
            is_js = server_script_path.endswith(".js")
            if not (is_python or is_js):
                raise ValueError("Server script must be a .py or .js file")

            command = "python" if is_python else "node"
            server_params = StdioServerParameters(
                command=command, args=[server_script_path], env=None
            )

            stdio_transport = await self.exit_stack.enter_async_context(
                stdio_client(server_params)
            )
            self.stdio, self.write = stdio_transport
            self.session = await self.exit_stack.enter_async_context(
                ClientSession(self.stdio, self.write)
            )

            await self.session.initialize()

            self.logger.info("Connected to MCP server")

            mcp_tools = await self.get_mcp_tools()
            self.tools = [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.inputSchema,
                }
                for tool in mcp_tools
            ]

            self.logger.info(
                f"Available tools: {[tool['name'] for tool in self.tools]}"
            )

            return True

        except Exception as e:
            self.logger.error(f"Error connecting to MCP server: {e}")
            traceback.print_exc()
            raise

    # connect to the Kubernetes MCP server
    async def connect_to_kubernetes_server(
        self, command: str, args: list, cluster_id: Optional[str] = None
    ):
        try:
            # Set up environment to include the local bin path
            env = os.environ.copy()
            env["PATH"] = f"{os.path.expanduser('~/.local/bin')}:{env.get('PATH', '')}"

            # Add kubeconfig environment variable if using a specific cluster
            if cluster_id:
                kubeconfig_path = self.cluster_manager.get_kubeconfig_path(cluster_id)
                if kubeconfig_path:
                    env["KUBECONFIG"] = kubeconfig_path
                    self.logger.info(f"Using kubeconfig: {kubeconfig_path}")

            server_params = StdioServerParameters(command=command, args=args, env=env)

            stdio_transport = await self.exit_stack.enter_async_context(
                stdio_client(server_params)
            )
            self.stdio, self.write = stdio_transport
            self.session = await self.exit_stack.enter_async_context(
                ClientSession(self.stdio, self.write)
            )

            await self.session.initialize()

            active_cluster = self.cluster_manager.get_active_cluster()
            cluster_name = active_cluster["name"] if active_cluster else "default"
            self.logger.info(
                f"Connected to Kubernetes MCP server (cluster: {cluster_name})"
            )

            mcp_tools = await self.get_mcp_tools()
            self.tools = [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.inputSchema,
                }
                for tool in mcp_tools
            ]

            self.logger.info(
                f"Available Kubernetes tools: {[tool['name'] for tool in self.tools]}"
            )

            return True

        except Exception as e:
            self.logger.error(f"Error connecting to Kubernetes MCP server: {e}")
            traceback.print_exc()
            raise

    # get mcp tool list
    async def get_mcp_tools(self):
        try:
            response = await self.session.list_tools()
            return response.tools
        except Exception as e:
            self.logger.error(f"Error getting MCP tools: {e}")
            raise

    # process query
    async def process_query(self, query: str):
        try:
            self.logger.info(f"Processing query: {query}")

            # Check if we need to reconnect with a different cluster context
            await self._ensure_correct_cluster_connection()

            # Don't clear conversation history - maintain context for conversational flow
            # Only clear messages for the current processing cycle
            self.messages = []

            user_message = {"role": "user", "content": query}

            # Add to persistent conversation history
            self.conversation_history.append(user_message)

            # Prepare messages for LLM (include conversation history + current message)
            self.messages = self.conversation_history.copy()

            while True:
                response = await self.call_llm()

                # the response is a text message
                if response.content[0].type == "text" and len(response.content) == 1:
                    # Let Claude handle the formatting naturally
                    assistant_message = {
                        "role": "assistant",
                        "content": response.content[0].text,
                    }
                    self.messages.append(assistant_message)
                    # Add to persistent conversation history
                    self.conversation_history.append(assistant_message)
                    await self.log_conversation()
                    break

                # the response is a tool call
                assistant_message = {
                    "role": "assistant",
                    "content": response.to_dict()["content"],
                }
                self.messages.append(assistant_message)
                # Add to persistent conversation history
                self.conversation_history.append(assistant_message)

                # Process all tool calls and collect results
                tool_results = []
                for content in response.content:
                    if content.type == "tool_use":
                        tool_name = content.name
                        tool_args = content.input
                        tool_use_id = content.id
                        self.logger.info(
                            f"Calling tool {tool_name} with args {tool_args}"
                        )
                        try:
                            assert self.session is not None
                            result = await self.session.call_tool(tool_name, tool_args)
                            self.logger.info(f"Tool {tool_name} result: {result}...")

                            # Store the tool results for formatting
                            self.last_tool_results = [
                                {"tool_name": tool_name, "result": result}
                            ]

                            # Collect tool result
                            tool_results.append(
                                {
                                    "type": "tool_result",
                                    "tool_use_id": tool_use_id,
                                    "content": result.content,
                                }
                            )
                        except Exception as e:
                            self.logger.error(f"Error calling tool {tool_name}: {e}")
                            # Add error result to maintain pairing
                            tool_results.append(
                                {
                                    "type": "tool_result",
                                    "tool_use_id": tool_use_id,
                                    "content": f"Error: {str(e)}",
                                    "is_error": True,
                                }
                            )

                # Add all tool results as a single message to maintain proper pairing
                if tool_results:
                    tool_result_message = {
                        "role": "user",
                        "content": tool_results,
                    }
                    self.messages.append(tool_result_message)
                    # Add to persistent conversation history
                    self.conversation_history.append(tool_result_message)

                await self.log_conversation()

            # Return the full conversation history for the frontend
            return self.conversation_history

        except Exception as e:
            self.logger.error(f"Error processing query: {e}")
            raise

    # call llm
    async def call_llm(self):
        try:
            self.logger.info("Calling LLM")

            # Add system prompt for natural interaction
            system_prompt = """You are a helpful Kubernetes assistant with access to MCP tools for interacting with Kubernetes clusters.

🚨 CRITICAL FORMATTING RULE: When showing ANY YAML, JSON, or code examples, you MUST wrap them in proper markdown code blocks with language specification:
- Use ```yaml for YAML configurations
- Use ```json for JSON data
- Use ```bash for shell commands
- NEVER show raw YAML/JSON without code block formatting

💬 **CRITICAL: Maintain Conversation Context**
- **Remember previous messages** in the conversation history
- **Understand contextual responses** like "yes", "no", "do it", "proceed", etc.
- **Reference previous questions** when users give short answers
- **Continue conversations naturally** without asking users to repeat information

Examples of contextual understanding:
- Previous: "Would you like me to fix the Flannel issue?" → User: "yes" → Proceed with the fix ✅
- Previous: "Should I create the pod with nginx:latest?" → User: "yes" → Create the pod ✅
- Previous: "Do you want me to delete this pod?" → User: "no" → Don't delete, ask what else to do ✅

🧠 **IMPORTANT: Be Smart About Tool Usage**
- **Knowledge/Explanation requests**: Answer directly with your expertise (no tools needed)
- **Action/Investigation requests**: Use MCP tools to interact with the cluster

Examples:
- "explain liveness probes" → Answer with your knowledge ✅
- "how to configure readiness probes" → Provide explanation ✅
- "what are my running pods?" → Use pods_list tool 🔧
- "delete pod nginx-123" → Use pods_delete tool 🔧
- "fix the flannel issue" → Use diagnostic tools 🔧

🔧 **CRITICAL: Resource Creation Requirements**
When users request to create Kubernetes resources (pods, deployments, services, etc.), you MUST:

1. **Ask for Required Parameters First** - Never create resources with default values without user confirmation
2. **Gather Essential Information** before proceeding:
   - **Pod Creation**: Ask for container image, name, namespace (optional), ports (if needed)
   - **Deployment Creation**: Ask for app name, container image, replicas, namespace
   - **Service Creation**: Ask for service type, ports, target pods/labels
   - **ConfigMap/Secret**: Ask for name, data/keys, namespace
   - **Any Resource**: Ask for namespace if not specified

3. **Interactive Parameter Collection**:
   ```
   User: "create a pod"
   Assistant: "I'd be happy to help you create a pod! To create it properly, I need some information:

   📋 **Required Information:**
   • **Container Image**: What image should the pod run? (e.g., nginx:latest, redis:alpine)
   • **Pod Name**: What should we name the pod?
   • **Namespace**: Which namespace? (default: 'default')
   • **Ports**: Does the container need to expose any ports?

   Please provide these details and I'll create the pod for you!"
   ```

4. **Only proceed with creation after getting user confirmation** of the parameters

5. **Resource-Specific Parameter Requirements**:
   - **Pods**: image (required), name, namespace, ports, environment variables
   - **Deployments**: app name, image, replicas, namespace, labels, selectors
   - **Services**: name, type (ClusterIP/NodePort/LoadBalancer), ports, selector labels
   - **ConfigMaps**: name, data keys and values, namespace
   - **Secrets**: name, secret type, data, namespace
   - **Ingress**: name, host rules, backend services, TLS settings
   - **PersistentVolumes**: size, access modes, storage class

6. **Never assume defaults** - Always ask the user to specify what they want

Key principles:
- 🎯 Understand the user's intent and respond appropriately
- 🧠 Use your Kubernetes expertise for explanations and guidance
- 🔧 Use MCP tools only when you need to interact with or inspect the actual cluster
- ❓ **Always ask for required parameters before creating resources**
- ✋ **Never create resources with assumed defaults without explicit user consent**
- 💬 Be conversational and helpful

Communication style:
- ✨ Use emojis strategically to make responses clear and easy to follow
- 📋 Structure information in digestible sections when showing complex data
- 🔍 Highlight important information (errors, warnings, key insights)
- 💡 Provide actionable suggestions when appropriate
- 🎨 Make technical information accessible and understandable

CRITICAL: When providing code examples (YAML, JSON, etc.):
- ALWAYS wrap ALL code in proper markdown code blocks with triple backticks
- ALWAYS specify the language: ```yaml for YAML, ```json for JSON, ```bash for commands
- NEVER show raw YAML/JSON without code block formatting
- Example of correct YAML formatting:
  ```yaml
  livenessProbe:
    httpGet:
      path: /health
      port: 8080
  ```
- Ensure proper indentation and formatting within code blocks
- Keep code examples clean and readable
- Use consistent spacing and structure

For complex operations (troubleshooting, fixing issues, multi-step processes):
- 📝 **Explain the plan first**: Before taking action, outline what you're going to do and why
- 🔄 **Progressive updates**: For multi-step operations, provide updates after each major step
- 🎯 **Clear progress indicators**: Use step numbers, checkmarks, or progress descriptions
- ⏱️ **Set expectations**: Let users know if something might take time
- 🔧 **Explain your reasoning**: Help users understand why you're taking specific actions

Example for complex requests:
"🔧 I'll help you fix the Flannel networking issue. Here's my plan:

📋 **Troubleshooting Plan:**
1. 🔍 First, I'll check the current pod status and logs
2. 🔎 Analyze the specific error messages
3. 🛠️ Apply the appropriate fix based on what I find
4. ✅ Verify the fix worked

Let me start by investigating the issue..."

Then provide updates like: "✅ Step 1 complete: Found the issue..." "🔄 Step 2 in progress..." etc.

You can handle requests like:
- 📊 Listing and analyzing resources
- ⚙️ Creating, updating, and deleting Kubernetes objects
- 🐳 Managing pods (logs, exec, resource usage)
- 📦 Helm operations
- 🔧 Cluster monitoring and troubleshooting

Always aim to be clear, helpful, and easy to understand. Use emojis to enhance clarity, not overwhelm."""

            return self.llm.messages.create(
                model="claude-3-5-haiku-20241022",
                max_tokens=1000,
                system=system_prompt,
                messages=self.messages,
                tools=self.tools,
            )
        except Exception as e:
            self.logger.error(f"Error calling LLM: {e}")
            raise

    def format_response_with_tool_usage(self, content):
        """Format the response to show MCP tool usage explicitly"""
        # Check if we have tool usage in the conversation
        tool_calls = []
        tool_results = []

        # Look through the conversation for tool calls and results
        for message in self.messages:
            if message["role"] == "assistant" and isinstance(message["content"], list):
                for item in message["content"]:
                    if isinstance(item, dict) and item.get("type") == "tool_use":
                        tool_calls.append(item)
            elif message["role"] == "user" and isinstance(message["content"], list):
                for item in message["content"]:
                    if isinstance(item, dict) and item.get("type") == "tool_result":
                        tool_results.append(item)

        # If we have tool usage, format the response accordingly
        if tool_calls and tool_results:
            formatted_response = "I'll use my native Kubernetes MCP tools to list all the pods in your cluster directly.\n\n"

            # Add tool usage display
            for tool_call in tool_calls:
                tool_name = tool_call.get("name", "unknown")
                formatted_response += f"Kubernetes_MCP_Server\n{tool_name}\noutput\n\n"

            # Add raw output
            for tool_result in tool_results:
                if tool_result.get("content"):
                    for result_content in tool_result["content"]:
                        # Handle both dict and TextContent objects
                        if hasattr(result_content, "text"):
                            formatted_response += result_content.text + "\n\n"
                        elif (
                            isinstance(result_content, dict)
                            and result_content.get("type") == "text"
                        ):
                            formatted_response += result_content["text"] + "\n\n"

            # Add the analysis
            formatted_response += content

            return formatted_response

        # If no tool usage, return original content
        return content

    def format_response_with_raw_output(self, content, tool_results):
        """Format the response to include raw tool output in the desired format"""

        # If we have tool results, format the response in the desired style
        if tool_results:
            formatted_response = "I'll use my native Kubernetes MCP tools to list all the pods in your cluster directly.\n\n"

            # Add tool usage display
            for tool_data in tool_results:
                tool_name = tool_data["tool_name"]
                result = tool_data["result"]

                formatted_response += f"Kubernetes_MCP_Server\n{tool_name}\noutput\n\n"

                # Format the output in a human-friendly way
                if result.content:
                    for result_content in result.content:
                        if hasattr(result_content, "text"):
                            formatted_output = (
                                self.format_kubernetes_data_human_friendly(
                                    result_content.text
                                )
                            )
                            formatted_response += formatted_output + "\n\n"

            # Add the analysis
            formatted_response += content

            return formatted_response

        # If no tool usage, return original content
        return content

    def format_kubernetes_data_human_friendly(self, raw_data):
        """Format raw Kubernetes table data into a human-friendly format"""
        lines = raw_data.strip().split("\n")
        if len(lines) < 2:
            return raw_data

        # Parse header
        header = lines[0]
        if not header.startswith("NAMESPACE"):
            return raw_data

        # Parse data rows
        pods_by_namespace = {}
        total_pods = 0

        for line in lines[1:]:
            if not line.strip():
                continue

            parts = line.split()
            if len(parts) < 8:
                continue

            namespace = parts[0]
            name = parts[3]
            ready = parts[4]
            status = parts[5]
            restarts = parts[6]
            age = parts[7]

            if namespace not in pods_by_namespace:
                pods_by_namespace[namespace] = []

            pods_by_namespace[namespace].append(
                {
                    "name": name,
                    "ready": ready,
                    "status": status,
                    "restarts": restarts,
                    "age": age,
                }
            )
            total_pods += 1

        # Format output
        formatted = "📊 **Kubernetes Pods Overview**\n"
        formatted += f"Total Pods: {total_pods}\n"
        formatted += f"Namespaces: {len(pods_by_namespace)}\n\n"

        # Sort namespaces by pod count
        sorted_namespaces = sorted(
            pods_by_namespace.items(), key=lambda x: len(x[1]), reverse=True
        )

        for namespace, pods in sorted_namespaces:
            formatted += f"🔹 **{namespace}** ({len(pods)} pods)\n"

            # Group by status
            status_groups = {}
            for pod in pods:
                status = pod["status"]
                if status not in status_groups:
                    status_groups[status] = []
                status_groups[status].append(pod)

            for status, status_pods in status_groups.items():
                status_icon = self.get_status_icon(status)
                formatted += f"  {status_icon} {status}: {len(status_pods)} pods\n"

                # Show first few pods as examples
                for pod in status_pods[:3]:
                    formatted += f"    • {pod['name']} (Ready: {pod['ready']}, Age: {pod['age']})\n"

                if len(status_pods) > 3:
                    formatted += f"    ... and {len(status_pods) - 3} more\n"

            formatted += "\n"

        return formatted

    def get_status_icon(self, status):
        """Get an appropriate icon for pod status"""
        status_icons = {
            "Running": "✅",
            "ContainerCreating": "🔄",
            "Pending": "⏳",
            "CrashLoopBackOff": "❌",
            "Error": "❌",
            "Failed": "❌",
            "Unknown": "❓",
            "Init:0/1": "🔄",
            "Completed": "✅",
            "Terminating": "🔄",
        }
        return status_icons.get(status, "📦")

    async def _ensure_correct_cluster_connection(self):
        """Ensure we're connected to the correct cluster based on active cluster setting"""
        try:
            active_cluster = self.cluster_manager.get_active_cluster()
            current_cluster_id = getattr(self, "_current_cluster_id", None)

            # Check if we need to switch clusters
            if active_cluster and active_cluster.get("id") != current_cluster_id:
                self.logger.info(f"Switching to cluster: {active_cluster['name']}")

                # Store current cluster ID
                self._current_cluster_id = active_cluster["id"]

                # Update the environment variable for the MCP server
                kubeconfig_path = self.cluster_manager.get_kubeconfig_path(
                    active_cluster["id"]
                )
                if kubeconfig_path:
                    import os

                    os.environ["KUBECONFIG"] = kubeconfig_path
                    self.logger.info(
                        f"Updated KUBECONFIG environment variable to: {kubeconfig_path}"
                    )
                elif active_cluster["type"] == "local":
                    # Remove KUBECONFIG to use default
                    import os

                    if "KUBECONFIG" in os.environ:
                        del os.environ["KUBECONFIG"]
                    self.logger.info(
                        "Using default kubectl configuration for local cluster"
                    )

        except Exception as e:
            self.logger.error(f"Error ensuring cluster connection: {e}")

    # cleanup
    async def cleanup(self):
        try:
            await self.exit_stack.aclose()
            self.logger.info("Disconnected from MCP server")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
            traceback.print_exc()
            raise

    def clear_conversation_history(self):
        """Clear the conversation history for a new chat session"""
        self.conversation_history = []
        self.messages = []
        self.last_tool_results = []
        self.logger.info("Conversation history cleared")

    async def log_conversation(self):
        os.makedirs("conversations", exist_ok=True)

        serializable_conversation = []

        for message in self.conversation_history:
            try:
                serializable_message = {"role": message["role"], "content": []}

                # Handle both string and list content
                if isinstance(message["content"], str):
                    serializable_message["content"] = message["content"]
                elif isinstance(message["content"], list):
                    for content_item in message["content"]:
                        if hasattr(content_item, "to_dict"):
                            serializable_message["content"].append(
                                content_item.to_dict()
                            )
                        elif hasattr(content_item, "dict"):
                            serializable_message["content"].append(content_item.dict())
                        elif hasattr(content_item, "model_dump"):
                            serializable_message["content"].append(
                                content_item.model_dump()
                            )
                        else:
                            serializable_message["content"].append(content_item)

                serializable_conversation.append(serializable_message)
            except Exception as e:
                self.logger.error(f"Error processing message: {str(e)}")
                self.logger.debug(f"Message content: {message}")
                raise

        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        filepath = os.path.join("conversations", f"conversation_{timestamp}.json")

        try:
            with open(filepath, "w") as f:
                json.dump(serializable_conversation, f, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"Error writing conversation to file: {str(e)}")
            self.logger.debug(f"Serializable conversation: {serializable_conversation}")
            raise
