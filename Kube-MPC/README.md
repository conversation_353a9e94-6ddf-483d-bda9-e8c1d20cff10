# Kubernetes MCP SaaS 🚀

A revolutionary **SaaS platform** that provides a conversational AI interface for managing Kubernetes clusters anywhere in the world. Built with the Model Context Protocol (MCP), it enables natural language interactions with your Kubernetes infrastructure through secure reverse proxy tunnels.

## 🌟 Key Features

### 🎯 **SaaS Architecture**
- **24/7 Hosted Service**: Run on your VPS/VM, accessible from anywhere
- **Zero Network Configuration**: Customers only need outbound internet access
- **Reverse Proxy Tunnels**: Secure WebSocket connections from customer clusters
- **Multi-Tenant**: Support unlimited customer clusters simultaneously

### 🤖 **Conversational AI Interface**
- **Natural Language**: "List all failing pods in production"
- **Context Awareness**: Maintains conversation history across operations
- **Smart Suggestions**: AI-powered recommendations and troubleshooting
- **Real-time Responses**: Instant feedback from cluster operations

### 🔒 **Enterprise Security**
- **JWT Authentication**: Secure token-based cluster authentication
- **Outbound-Only**: No inbound firewall rules required
- **RBAC Integration**: Respects Kubernetes role-based access controls
- **Encrypted Tunnels**: All communications over secure WebSocket/TLS

### 🌐 **Universal Cluster Support**
- **Cloud Providers**: AWS EKS, Google GKE, Azure AKS, DigitalOcean
- **On-Premises**: Self-managed Kubernetes clusters
- **Hybrid Environments**: Mix of cloud and on-premises
- **Private Networks**: VPN-connected or air-gapped clusters

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Your SaaS     │    │  Customer K8s    │    │   Web Browser   │
│   (VPS/VM)      │    │    Cluster       │    │                 │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │   WebApp    │ │    │ │ Proxy Pod    │ │    │ │    User     │ │
│ │             │ │◄───┤ │              │ │    │ │ Interface   │ │
│ └─────────────┘ │    │ │   kubectl    │ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ │              │ │    └─────────────────┘
│ │ Tunnel      │ │    │ └──────────────┘ │              │
│ │ Server      │ │    │        │         │              │
│ │             │ │◄───┼────────┘         │              │
│ └─────────────┘ │    │ Outbound Only    │              │
│ ┌─────────────┐ │    │ (Port 443/80)    │              │
│ │ MCP Client  │ │    └──────────────────┘              │
│ │             │ │                                       │
│ └─────────────┘ │◄──────────────────────────────────────┘
└─────────────────┘           HTTPS/WSS
```

## 🚀 Quick Start

### For SaaS Providers (You)

1. **Deploy the SaaS Platform**:
```bash
git clone https://github.com/your-username/kubernetes-mcp-saas.git
cd kubernetes-mcp-saas

# Install dependencies
uv sync

# Start the services
uv run python api/main.py          # Web interface (port 8000)
uv run python api/tunnel_server.py # Tunnel server (port 8001)
```

2. **Access the Platform**:
   - Open `http://your-vps-ip:8000` for the web interface
   - Tunnel server runs on `ws://your-vps-ip:8001`

### For Customers (Cluster Owners)

1. **Get Your Cluster Token**:
   - Contact your SaaS provider for a cluster token
   - Or generate one through the SaaS web interface

2. **Deploy the Proxy Pod**:
```bash
# Add the Helm repository
helm repo add kubernetes-mcp-saas https://your-domain.com/helm-charts

# Install the proxy
helm install mcp-proxy kubernetes-mcp-saas/kubernetes-mcp-proxy \
  --set proxy.saasUrl="wss://your-saas-domain.com/tunnel" \
  --set-string proxy.clusterToken="your-cluster-token"
```

3. **That's It!** 🎉
   - Your cluster is now connected to the SaaS
   - Start managing it through the web interface

## 📦 What's Included

### **SaaS Backend**
- `api/main.py` - FastAPI web server with chat interface
- `api/tunnel_server.py` - WebSocket tunnel server for cluster connections
- `api/mcp_client.py` - MCP client with conversational AI
- `api/cluster_manager.py` - Multi-cluster management system

### **Customer Deployment**
- `cluster-proxy/` - Proxy pod that runs in customer clusters
- `helm-chart/` - Helm chart for easy customer deployment
- `Dockerfile` - Container image for the proxy pod

### **Documentation**
- `REMOTE_CLUSTERS.md` - Detailed setup guide for remote clusters
- `README.md` - This comprehensive overview

## 🛠️ Development Setup

### Prerequisites
- Python 3.11+
- uv (Python package manager)
- Docker (for building proxy images)
- Helm (for chart development)

### Local Development
```bash
# Clone the repository
git clone https://github.com/your-username/kubernetes-mcp-saas.git
cd kubernetes-mcp-saas

# Install dependencies
uv sync

# Start development servers
uv run python api/main.py &          # Web interface
uv run python api/tunnel_server.py & # Tunnel server

# Build proxy image
cd cluster-proxy
docker build -t your-registry/kubernetes-mcp-proxy:latest .
```

## 🌍 Deployment Guide

### **SaaS Platform Deployment**

1. **VPS/VM Requirements**:
   - 2+ CPU cores, 4GB+ RAM
   - Ubuntu 20.04+ or similar
   - Public IP address
   - Ports 80, 443, 8000, 8001 accessible

2. **Domain Setup**:
   - Point your domain to the VPS IP
   - Set up SSL certificates (Let's Encrypt recommended)
   - Configure reverse proxy (nginx/traefik)

3. **Production Configuration**:
```bash
# Environment variables
export SAAS_DOMAIN="your-saas-domain.com"
export JWT_SECRET="your-secret-key"
export DATABASE_URL="postgresql://..."  # Optional

# Start services with process manager
pm2 start api/main.py --name "mcp-web"
pm2 start api/tunnel_server.py --name "mcp-tunnel"
```

### **Customer Onboarding**

1. **Generate Cluster Token**:
```python
# In your SaaS admin interface
token = tunnel_server.generate_cluster_token(
    cluster_id="customer-cluster-1",
    cluster_name="Customer Production Cluster"
)
```

2. **Provide Helm Installation**:
```bash
# Customer runs this command
helm install mcp-proxy kubernetes-mcp-saas/kubernetes-mcp-proxy \
  --set proxy.saasUrl="wss://your-saas-domain.com/tunnel" \
  --set-string proxy.clusterToken="<generated-token>"
```

## 🔧 Configuration

### **SaaS Configuration**
```yaml
# config.yaml
saas:
  domain: "your-saas-domain.com"
  web_port: 8000
  tunnel_port: 8001
  jwt_secret: "your-secret-key"
  
database:
  url: "postgresql://user:pass@localhost/mcpsaas"
  
monitoring:
  enabled: true
  prometheus_port: 9090
```

### **Customer Configuration**
```yaml
# values.yaml for Helm chart
proxy:
  saasUrl: "wss://your-saas-domain.com/tunnel"
  clusterToken: "your-cluster-token"
  clusterName: "Production Cluster"
  
rbac:
  clusterWide: true  # Full cluster access
  
resources:
  limits:
    cpu: 200m
    memory: 256Mi
```

## 🚀 Business Model

### **Revenue Streams**
- **Per-Cluster Pricing**: $29/month per connected cluster
- **Enterprise Plans**: Custom pricing for large deployments
- **Professional Services**: Setup and integration assistance

### **Value Proposition**
- **Zero Setup Complexity**: Customers just run `helm install`
- **Universal Compatibility**: Works with any Kubernetes cluster
- **AI-Powered Management**: Natural language cluster operations
- **24/7 Availability**: Always-on SaaS platform

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### **Development Workflow**
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.your-saas-domain.com](https://docs.your-saas-domain.com)
- **Issues**: [GitHub Issues](https://github.com/your-username/kubernetes-mcp-saas/issues)
- **Email**: <EMAIL>
- **Discord**: [Join our community](https://discord.gg/your-invite)

---

**Ready to revolutionize Kubernetes management?** 🚀

Deploy your SaaS platform today and start connecting customer clusters worldwide!
