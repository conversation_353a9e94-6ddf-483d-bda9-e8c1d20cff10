# Contributing to Kubernetes MCP SaaS

We love your input! We want to make contributing to Kubernetes MCP SaaS as easy and transparent as possible, whether it's:

- Reporting a bug
- Discussing the current state of the code
- Submitting a fix
- Proposing new features
- Becoming a maintainer

## Development Process

We use GitHub to host code, to track issues and feature requests, as well as accept pull requests.

## Pull Requests

Pull requests are the best way to propose changes to the codebase. We actively welcome your pull requests:

1. Fork the repo and create your branch from `main`.
2. If you've added code that should be tested, add tests.
3. If you've changed APIs, update the documentation.
4. Ensure the test suite passes.
5. Make sure your code lints.
6. Issue that pull request!

## Any contributions you make will be under the MIT Software License

In short, when you submit code changes, your submissions are understood to be under the same [MIT License](http://choosealicense.com/licenses/mit/) that covers the project. Feel free to contact the maintainers if that's a concern.

## Report bugs using GitHub's [issue tracker](https://github.com/your-username/kubernetes-mcp-saas/issues)

We use GitHub issues to track public bugs. Report a bug by [opening a new issue](https://github.com/your-username/kubernetes-mcp-saas/issues/new); it's that easy!

## Write bug reports with detail, background, and sample code

**Great Bug Reports** tend to have:

- A quick summary and/or background
- Steps to reproduce
  - Be specific!
  - Give sample code if you can
- What you expected would happen
- What actually happens
- Notes (possibly including why you think this might be happening, or stuff you tried that didn't work)

## Development Setup

1. **Clone the repository**:
```bash
git clone https://github.com/your-username/kubernetes-mcp-saas.git
cd kubernetes-mcp-saas
```

2. **Install dependencies**:
```bash
uv sync
```

3. **Set up pre-commit hooks**:
```bash
pre-commit install
```

4. **Run tests**:
```bash
uv run pytest
```

5. **Start development servers**:
```bash
uv run python api/main.py &          # Web interface
uv run python api/tunnel_server.py & # Tunnel server
```

## Code Style

We use several tools to maintain code quality:

- **Black** for code formatting
- **isort** for import sorting
- **flake8** for linting
- **mypy** for type checking

Run all checks with:
```bash
uv run pre-commit run --all-files
```

## Testing

We use pytest for testing. Please add tests for any new functionality:

```bash
# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov=api

# Run specific test file
uv run pytest tests/test_tunnel_server.py
```

## Documentation

- Update docstrings for any new functions/classes
- Update README.md if you change functionality
- Add examples for new features

## Commit Messages

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation changes
- `style:` for formatting changes
- `refactor:` for code refactoring
- `test:` for adding tests
- `chore:` for maintenance tasks

Examples:
```
feat: add cluster health monitoring
fix: resolve WebSocket connection timeout
docs: update deployment guide
```

## Project Structure

```
kubernetes-mcp-saas/
├── api/                    # SaaS backend
│   ├── main.py            # Web server
│   ├── tunnel_server.py   # Tunnel server
│   ├── mcp_client.py      # MCP client
│   └── cluster_manager.py # Cluster management
├── cluster-proxy/         # Customer proxy pod
│   ├── proxy.py          # Proxy implementation
│   ├── Dockerfile        # Container image
│   └── requirements.txt  # Dependencies
├── helm-chart/           # Helm chart for customers
│   ├── Chart.yaml       # Chart metadata
│   ├── values.yaml      # Default values
│   └── templates/       # Kubernetes templates
├── static/              # Frontend assets
├── tests/               # Test files
└── docs/                # Documentation
```

## Release Process

1. Update version in `pyproject.toml`
2. Update `CHANGELOG.md`
3. Create a pull request
4. After merge, create a GitHub release
5. Automated CI/CD will build and publish

## Questions?

Feel free to open an issue or reach out to the maintainers directly.

## License

By contributing, you agree that your contributions will be licensed under the MIT License.
