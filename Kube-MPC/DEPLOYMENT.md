# Deployment Guide

This guide covers deploying the Kubernetes MCP SaaS platform for production use.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                        Your SaaS Platform                      │
│                         (VPS/VM/Cloud)                         │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │   Nginx     │  │  Web App    │  │   Tunnel    │            │
│  │   Proxy     │  │  (Port      │  │   Server    │            │
│  │ (Port 443)  │  │   8000)     │  │ (Port 8001) │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
└─────────────────────────────────────────────────────────────────┘
                              │
                              │ Secure WebSocket Tunnels
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Customer Clusters                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   AWS EKS       │  │   Google GKE    │  │  On-Premises    │ │
│  │ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │ │
│  │ │ Proxy Pod   │ │  │ │ Proxy Pod   │ │  │ │ Proxy Pod   │ │ │
│  │ └─────────────┘ │  │ └─────────────┘ │  │ └─────────────┘ │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 🚀 SaaS Platform Deployment

### Prerequisites

- **Server**: VPS/VM with 2+ CPU cores, 4GB+ RAM
- **OS**: Ubuntu 20.04+ or similar Linux distribution
- **Domain**: Registered domain name pointing to your server
- **SSL**: SSL certificate (Let's Encrypt recommended)
- **Ports**: 80, 443, 8000, 8001 accessible from internet

### Step 1: Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y nginx certbot python3-certbot-nginx git curl

# Install uv (Python package manager)
curl -LsSf https://astral.sh/uv/install.sh | sh
source ~/.bashrc

# Install PM2 for process management
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
sudo npm install -g pm2
```

### Step 2: Clone and Setup Application

```bash
# Clone repository
git clone https://github.com/your-username/kubernetes-mcp-saas.git
cd kubernetes-mcp-saas

# Install dependencies
uv sync

# Create environment file
cp .env.example .env
```

### Step 3: Configure Environment

Edit `.env` file:

```bash
# Domain configuration
SAAS_DOMAIN=your-saas-domain.com
WEB_PORT=8000
TUNNEL_PORT=8001

# Security
JWT_SECRET=your-super-secret-jwt-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key

# Database (optional)
DATABASE_URL=postgresql://user:password@localhost/mcpsaas

# Monitoring (optional)
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
```

### Step 4: SSL Certificate Setup

```bash
# Get SSL certificate
sudo certbot --nginx -d your-saas-domain.com

# Verify auto-renewal
sudo certbot renew --dry-run
```

### Step 5: Nginx Configuration

Create `/etc/nginx/sites-available/mcp-saas`:

```nginx
server {
    listen 80;
    server_name your-saas-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-saas-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-saas-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-saas-domain.com/privkey.pem;

    # Web interface
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket tunnel endpoint
    location /tunnel {
        proxy_pass http://127.0.0.1:8001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/mcp-saas /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Step 6: Start Services

```bash
# Start web application
pm2 start "uv run python api/main.py" --name "mcp-web"

# Start tunnel server
pm2 start "uv run python api/tunnel_server.py" --name "mcp-tunnel"

# Save PM2 configuration
pm2 save
pm2 startup
```

### Step 7: Verify Deployment

```bash
# Check services
pm2 status

# Check logs
pm2 logs mcp-web
pm2 logs mcp-tunnel

# Test web interface
curl https://your-saas-domain.com

# Test tunnel endpoint
curl -H "Upgrade: websocket" https://your-saas-domain.com/tunnel
```

## 🐳 Docker Deployment (Alternative)

### Docker Compose Setup

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - SAAS_DOMAIN=your-saas-domain.com
      - JWT_SECRET=your-secret-key
      - ANTHROPIC_API_KEY=your-api-key
    volumes:
      - ./clusters:/app/clusters
    restart: unless-stopped

  tunnel:
    build: .
    command: python api/tunnel_server.py
    ports:
      - "8001:8001"
    environment:
      - JWT_SECRET=your-secret-key
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - /etc/letsencrypt:/etc/letsencrypt
    depends_on:
      - web
      - tunnel
    restart: unless-stopped
```

Deploy:
```bash
docker-compose up -d
```

## 🔧 Customer Onboarding

### Generate Cluster Token

```python
# In Python shell or admin interface
from api.tunnel_server import tunnel_server

token = tunnel_server.generate_cluster_token(
    cluster_id="customer-prod-cluster",
    cluster_name="Customer Production Cluster"
)

print(f"Cluster Token: {token}")
```

### Provide Installation Instructions

Give customers this command:

```bash
# Add Helm repository (if you host charts)
helm repo add mcp-saas https://your-saas-domain.com/helm-charts

# Install proxy pod
helm install mcp-proxy mcp-saas/kubernetes-mcp-proxy \
  --set proxy.saasUrl="wss://your-saas-domain.com/tunnel" \
  --set-string proxy.clusterToken="<GENERATED_TOKEN>"
```

## 📊 Monitoring and Maintenance

### Health Checks

```bash
# Check service health
curl https://your-saas-domain.com/health

# Check tunnel server
curl https://your-saas-domain.com/tunnel/health

# Monitor connected clusters
curl https://your-saas-domain.com/api/clusters
```

### Log Management

```bash
# View application logs
pm2 logs mcp-web --lines 100

# View tunnel server logs
pm2 logs mcp-tunnel --lines 100

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### Backup Strategy

```bash
# Backup cluster configurations
tar -czf clusters-backup-$(date +%Y%m%d).tar.gz clusters/

# Backup environment configuration
cp .env .env.backup

# Database backup (if using PostgreSQL)
pg_dump mcpsaas > mcpsaas-backup-$(date +%Y%m%d).sql
```

### Updates

```bash
# Pull latest code
git pull origin main

# Update dependencies
uv sync

# Restart services
pm2 restart all

# Check status
pm2 status
```

## 🔒 Security Considerations

### Firewall Configuration

```bash
# Allow SSH, HTTP, HTTPS
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

# Block direct access to application ports
sudo ufw deny 8000
sudo ufw deny 8001

# Enable firewall
sudo ufw enable
```

### SSL/TLS Security

- Use strong SSL certificates (Let's Encrypt or commercial)
- Enable HTTP/2 for better performance
- Configure proper SSL headers
- Regular certificate renewal

### Application Security

- Use strong JWT secrets
- Regularly rotate API keys
- Monitor for suspicious activity
- Keep dependencies updated

## 📈 Scaling

### Horizontal Scaling

- Use load balancer (nginx, HAProxy, or cloud LB)
- Deploy multiple web app instances
- Use Redis for session storage
- Database clustering for high availability

### Vertical Scaling

- Monitor resource usage
- Scale CPU/RAM as needed
- Optimize database queries
- Use caching (Redis/Memcached)

## 🆘 Troubleshooting

### Common Issues

1. **WebSocket Connection Fails**
   - Check nginx WebSocket configuration
   - Verify SSL certificate
   - Check firewall rules

2. **Cluster Proxy Can't Connect**
   - Verify tunnel server is running
   - Check JWT token validity
   - Confirm outbound connectivity from cluster

3. **High Memory Usage**
   - Monitor conversation history storage
   - Implement cleanup for old sessions
   - Check for memory leaks

### Support

- Check logs first: `pm2 logs`
- Monitor system resources: `htop`, `df -h`
- Test connectivity: `curl`, `telnet`
- Review nginx logs: `/var/log/nginx/`

---

**Your SaaS platform is now ready to serve customers worldwide!** 🚀
