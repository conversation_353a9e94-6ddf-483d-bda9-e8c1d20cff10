#!/usr/bin/env python3
"""
Cluster Proxy - Runs inside customer's Kubernetes cluster
Establishes outbound tunnel to SaaS and executes kubectl commands
"""

import asyncio
import json
import logging
import os
import sys
from typing import Dict, Optional
import websockets
from websockets.client import WebSocketClientProtocol
import signal

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ClusterProxy:
    def __init__(self):
        # Configuration from environment variables
        self.saas_url = os.getenv("SAAS_TUNNEL_URL", "ws://localhost:8001")
        self.cluster_token = os.getenv("CLUSTER_TOKEN")
        self.cluster_id = os.getenv("CLUSTER_ID")
        self.cluster_name = os.getenv("CLUSTER_NAME", "unknown-cluster")
        self.reconnect_interval = int(os.getenv("RECONNECT_INTERVAL", "30"))
        self.ping_interval = int(os.getenv("PING_INTERVAL", "30"))

        self.websocket: Optional[WebSocketClientProtocol] = None
        self.running = True

        if not self.cluster_token:
            logger.error("CLUSTER_TOKEN environment variable is required")
            sys.exit(1)

        if not self.cluster_id:
            logger.error("CLUSTER_ID environment variable is required")
            sys.exit(1)

    async def connect_to_saas(self):
        """Establish WebSocket connection to SaaS tunnel server"""
        try:
            logger.info(f"Connecting to SaaS tunnel server: {self.saas_url}")

            self.websocket = await websockets.connect(
                self.saas_url,
                ping_interval=self.ping_interval,
                ping_timeout=10,
                close_timeout=10,
            )

            # Send authentication
            auth_message = {
                "type": "auth",
                "token": self.cluster_token,
                "cluster_id": self.cluster_id,
                "cluster_name": self.cluster_name,
            }

            await self.websocket.send(json.dumps(auth_message))

            # Wait for auth response
            response = await asyncio.wait_for(self.websocket.recv(), timeout=30.0)
            auth_response = json.loads(response)

            if auth_response.get("type") == "auth_success":
                logger.info(
                    f"Successfully authenticated with SaaS: {auth_response.get('message')}"
                )
                return True
            else:
                logger.error(f"Authentication failed: {auth_response.get('message')}")
                return False

        except Exception as e:
            logger.error(f"Failed to connect to SaaS: {e}")
            return False

    async def handle_messages(self):
        """Handle incoming messages from SaaS"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.process_message(data)
                except json.JSONDecodeError:
                    logger.error("Received invalid JSON message")
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
        except websockets.exceptions.ConnectionClosed:
            logger.warning("Connection to SaaS closed")
        except Exception as e:
            logger.error(f"Error in message handler: {e}")

    async def process_message(self, data: Dict):
        """Process incoming message from SaaS"""
        message_type = data.get("type")

        if message_type == "ping":
            # Respond to ping
            await self.websocket.send(json.dumps({"type": "pong"}))

        elif message_type == "kubectl_command":
            # Execute kubectl command
            await self.execute_kubectl_command(data)

        else:
            logger.warning(f"Unknown message type: {message_type}")

    async def execute_kubectl_command(self, command_data: Dict):
        """Execute kubectl command and send response back to SaaS"""
        request_id = command_data.get("request_id")
        command = command_data.get("command", [])
        timeout = command_data.get("timeout", 30)

        logger.info(
            f"Executing kubectl command: {' '.join(command)} (request_id: {request_id})"
        )

        try:
            # Execute kubectl command
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="/",
                env=os.environ.copy(),
            )

            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), timeout=timeout
                )

                return_code = process.returncode

                # Prepare response
                response = {
                    "type": "kubectl_response",
                    "request_id": request_id,
                    "return_code": return_code,
                    "stdout": stdout.decode("utf-8", errors="replace"),
                    "stderr": stderr.decode("utf-8", errors="replace"),
                    "success": return_code == 0,
                }

                # Send response back to SaaS
                await self.websocket.send(json.dumps(response))

                logger.info(
                    f"Command completed with return code {return_code} (request_id: {request_id})"
                )

            except asyncio.TimeoutError:
                # Kill the process if it times out
                try:
                    process.kill()
                    await process.wait()
                except Exception:
                    pass

                response = {
                    "type": "kubectl_response",
                    "request_id": request_id,
                    "return_code": -1,
                    "stdout": "",
                    "stderr": f"Command timed out after {timeout} seconds",
                    "success": False,
                    "timeout": True,
                }

                await self.websocket.send(json.dumps(response))
                logger.warning(f"Command timed out (request_id: {request_id})")

        except Exception as e:
            logger.error(f"Error executing kubectl command: {e}")

            response = {
                "type": "kubectl_response",
                "request_id": request_id,
                "return_code": -1,
                "stdout": "",
                "stderr": f"Error executing command: {str(e)}",
                "success": False,
                "error": str(e),
            }

            try:
                await self.websocket.send(json.dumps(response))
            except Exception:
                logger.error("Failed to send error response")

    async def send_periodic_ping(self):
        """Send periodic ping to keep connection alive"""
        while self.running and self.websocket:
            try:
                await asyncio.sleep(self.ping_interval)
                if self.websocket and not self.websocket.closed:
                    await self.websocket.send(json.dumps({"type": "ping"}))
                    logger.debug("Sent ping to SaaS")
            except Exception as e:
                logger.error(f"Error sending ping: {e}")
                break

    async def run(self):
        """Main run loop with reconnection logic"""
        logger.info(
            f"Starting cluster proxy for {self.cluster_name} ({self.cluster_id})"
        )

        while self.running:
            try:
                # Connect to SaaS
                if await self.connect_to_saas():
                    # Start ping task
                    ping_task = asyncio.create_task(self.send_periodic_ping())

                    try:
                        # Handle messages
                        await self.handle_messages()
                    finally:
                        ping_task.cancel()
                        try:
                            await ping_task
                        except asyncio.CancelledError:
                            pass

                # Close connection
                if self.websocket:
                    await self.websocket.close()
                    self.websocket = None

                if self.running:
                    logger.info(f"Reconnecting in {self.reconnect_interval} seconds...")
                    await asyncio.sleep(self.reconnect_interval)

            except KeyboardInterrupt:
                logger.info("Received interrupt signal, shutting down...")
                self.running = False
            except Exception as e:
                logger.error(f"Unexpected error in main loop: {e}")
                if self.running:
                    await asyncio.sleep(self.reconnect_interval)

    def stop(self):
        """Stop the proxy"""
        logger.info("Stopping cluster proxy...")
        self.running = False


def signal_handler(proxy):
    """Handle shutdown signals"""

    def handler(signum, frame):
        logger.info(f"Received signal {signum}")
        proxy.stop()

    return handler


async def main():
    """Main entry point"""
    proxy = ClusterProxy()

    # Set up signal handlers
    signal.signal(signal.SIGTERM, signal_handler(proxy))
    signal.signal(signal.SIGINT, signal_handler(proxy))

    try:
        await proxy.run()
    except KeyboardInterrupt:
        logger.info("Shutting down...")
    finally:
        if proxy.websocket:
            await proxy.websocket.close()


if __name__ == "__main__":
    asyncio.run(main())
