{{- if .Values.rbac.create -}}
{{- if .Values.rbac.clusterWide }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "kubernetes-mcp-proxy.fullname" . }}
  labels:
    {{- include "kubernetes-mcp-proxy.labels" . | nindent 4 }}
rules:
  # Full cluster access for kubectl operations
  - apiGroups: ["*"]
    resources: ["*"]
    verbs: ["*"]
  # Non-resource URLs
  - nonResourceURLs: ["*"]
    verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "kubernetes-mcp-proxy.fullname" . }}
  labels:
    {{- include "kubernetes-mcp-proxy.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "kubernetes-mcp-proxy.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "kubernetes-mcp-proxy.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
{{- else }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: {{ .Release.Namespace }}
  name: {{ include "kubernetes-mcp-proxy.fullname" . }}
  labels:
    {{- include "kubernetes-mcp-proxy.labels" . | nindent 4 }}
rules:
  # Namespace-scoped access
  - apiGroups: [""]
    resources: ["*"]
    verbs: ["*"]
  - apiGroups: ["apps"]
    resources: ["*"]
    verbs: ["*"]
  - apiGroups: ["extensions"]
    resources: ["*"]
    verbs: ["*"]
  - apiGroups: ["networking.k8s.io"]
    resources: ["*"]
    verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "kubernetes-mcp-proxy.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "kubernetes-mcp-proxy.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "kubernetes-mcp-proxy.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "kubernetes-mcp-proxy.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
{{- end }}
{{- end }}
