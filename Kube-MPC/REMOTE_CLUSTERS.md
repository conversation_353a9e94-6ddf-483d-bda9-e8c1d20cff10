# Remote Kubernetes Cluster Management

This guide explains how to use the Kubernetes MCP Client with remote clusters in different public/private networks.

## 🏗️ Architecture

The enhanced MCP Client now supports multiple Kubernetes clusters through kubeconfig management:

```
[WebApp] → [MCP Server] → [kubectl + kubeconfig] → [Remote K8s Clusters]
                ↓
        [Cluster Manager]
        ├── Local Cluster (auto-detected)
        ├── Remote Cluster 1 (via kubeconfig)
        ├── Remote Cluster 2 (via kubeconfig)
        └── Remote Cluster N (via kubeconfig)
```

## 🚀 Getting Started

### Step 1: Obtain Kubeconfig Files

For each remote cluster, you need a kubeconfig file. Here's how to get them:

#### Cloud Providers

**AWS EKS:**
```bash
aws eks update-kubeconfig --region us-west-2 --name my-cluster
# Copy ~/.kube/config or create a specific config file
```

**Google GKE:**
```bash
gcloud container clusters get-credentials my-cluster --zone us-central1-a
# Copy ~/.kube/config or create a specific config file
```

**Azure AKS:**
```bash
az aks get-credentials --resource-group myResourceGroup --name myAKSCluster
# Copy ~/.kube/config or create a specific config file
```

**DigitalOcean:**
```bash
doctl kubernetes cluster kubeconfig save my-cluster
# Copy ~/.kube/config or create a specific config file
```

#### Self-Managed Clusters

1. **Copy from master node:**
   ```bash
   scp user@master-node:/etc/kubernetes/admin.conf ./my-cluster-config.yaml
   ```

2. **Create service account kubeconfig:**
   ```bash
   # Create service account and get token
   kubectl create serviceaccount mcp-client
   kubectl create clusterrolebinding mcp-client --clusterrole=cluster-admin --serviceaccount=default:mcp-client
   
   # Get token and create kubeconfig
   TOKEN=$(kubectl get secret $(kubectl get serviceaccount mcp-client -o jsonpath='{.secrets[0].name}') -o jsonpath='{.data.token}' | base64 --decode)
   
   # Create kubeconfig file with token
   ```

### Step 2: Upload Kubeconfig to MCP Client

1. **Open the MCP Client interface** at `http://localhost:8000`

2. **Upload kubeconfig file:**
   - **Drag & Drop:** Simply drag your kubeconfig file to the upload area in the sidebar
   - **Click Upload:** Click the upload area and select your kubeconfig file

3. **Automatic Processing:**
   - The system validates the kubeconfig format
   - Tests connectivity to the cluster
   - Shows real-time status (🟢 online, 🔴 offline, 🟡 timeout)

### Step 3: Switch Between Clusters

1. **View available clusters** in the sidebar
2. **Click any cluster** to activate it
3. **Confirmation:** You'll see a notification confirming the cluster switch
4. **Use normally:** All subsequent operations will use the selected cluster

## 🔧 Features

### Cluster Management
- **Auto-detection:** Local clusters are automatically detected
- **Validation:** All kubeconfig files are validated before use
- **Status monitoring:** Real-time connectivity status for each cluster
- **Secure storage:** Kubeconfig files are stored securely

### User Interface
- **Visual indicators:** Color-coded status for each cluster
- **Cluster details:** Shows cluster name, context, server URL, and status
- **Management actions:** Refresh status, delete clusters, activate clusters
- **Drag & drop:** Easy kubeconfig file upload

### Network Support
- **Public clouds:** Works with all major cloud providers
- **Private networks:** Supports VPN and private network access
- **On-premises:** Compatible with self-managed clusters
- **Hybrid environments:** Mix local and remote clusters

## 🔒 Security

### Network Requirements
- **Connectivity:** MCP server host must reach cluster API endpoints
- **Authentication:** Valid certificates/tokens in kubeconfig
- **Firewall:** Allow outbound HTTPS (443) to cluster endpoints
- **DNS:** Cluster endpoints must be resolvable

### Security Features
- **Kubeconfig validation:** All files validated before storage
- **Secure storage:** Files stored with restricted permissions
- **Context isolation:** Each cluster operates independently
- **Clean deletion:** Secure removal of cluster configurations

## 🌐 Network Scenarios

### Public Cloud Clusters
- **Requirements:** Valid kubeconfig with cloud provider credentials
- **Connectivity:** Usually works out-of-the-box
- **Authentication:** Handled by cloud provider tokens/certificates

### Private Network Clusters
- **Requirements:** Network connectivity (VPN, peering, etc.)
- **Connectivity:** May need additional network configuration
- **Authentication:** Standard kubeconfig authentication

### On-Premises Clusters
- **Requirements:** Network access and proper certificates
- **Connectivity:** Direct network access or VPN required
- **Authentication:** Certificate-based or token-based

### Behind NAT/Firewall
- **Requirements:** Port forwarding or proxy configuration
- **Connectivity:** May need additional network setup
- **Authentication:** Standard kubeconfig authentication

## 📊 Status Indicators

- 🟢 **Online:** Cluster is reachable and responding
- 🔴 **Offline:** Cluster is unreachable or authentication failed
- 🟡 **Timeout:** Cluster connection timed out
- ⚠️ **Error:** Configuration or network error
- 🔄 **Refreshing:** Currently testing connectivity

## 🧪 Testing

### Test Connectivity
1. Click the refresh button (🔄) next to any cluster
2. Watch the status indicator update
3. Check the notification for detailed results

### Test Operations
1. Select a cluster
2. Send a query like "list pods"
3. Verify the results are from the correct cluster

## 🔧 Troubleshooting

### Common Issues

**"Cluster offline" status:**
- Check network connectivity to cluster endpoint
- Verify kubeconfig credentials are valid
- Ensure firewall allows outbound HTTPS traffic

**"Authentication failed":**
- Check if kubeconfig tokens/certificates are expired
- Verify service account has proper permissions
- Re-download kubeconfig from cluster provider

**"Connection timeout":**
- Check if cluster endpoint is reachable
- Verify DNS resolution of cluster endpoint
- Check for network latency issues

### Debug Steps

1. **Check kubeconfig manually:**
   ```bash
   kubectl --kubeconfig=/path/to/config cluster-info
   ```

2. **Test network connectivity:**
   ```bash
   curl -k https://your-cluster-endpoint:6443/version
   ```

3. **Verify DNS resolution:**
   ```bash
   nslookup your-cluster-endpoint
   ```

## 🎯 Best Practices

1. **Organize kubeconfigs:** Use descriptive names for cluster files
2. **Regular updates:** Keep kubeconfig credentials up to date
3. **Monitor status:** Regularly check cluster connectivity status
4. **Secure storage:** Don't share kubeconfig files unnecessarily
5. **Test connectivity:** Verify new clusters work before relying on them

## 🚀 Next Steps

With remote cluster support, you can now:
- Manage development, staging, and production clusters
- Work with multi-cloud deployments
- Handle hybrid on-premises and cloud environments
- Switch between different customer environments
- Maintain separate contexts for different projects

The MCP Client now provides a unified interface for all your Kubernetes clusters, regardless of where they're running!
