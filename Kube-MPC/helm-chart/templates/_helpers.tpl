{{/*
Expand the name of the chart.
*/}}
{{- define "kubernetes-mcp-proxy.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
*/}}
{{- define "kubernetes-mcp-proxy.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "kubernetes-mcp-proxy.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "kubernetes-mcp-proxy.labels" -}}
helm.sh/chart: {{ include "kubernetes-mcp-proxy.chart" . }}
{{ include "kubernetes-mcp-proxy.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "kubernetes-mcp-proxy.selectorLabels" -}}
app.kubernetes.io/name: {{ include "kubernetes-mcp-proxy.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "kubernetes-mcp-proxy.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "kubernetes-mcp-proxy.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Generate cluster ID if not provided
*/}}
{{- define "kubernetes-mcp-proxy.clusterId" -}}
{{- if .Values.proxy.clusterId }}
{{- .Values.proxy.clusterId }}
{{- else }}
{{- printf "%s-%s" .Release.Namespace .Release.Name | sha256sum | trunc 8 }}
{{- end }}
{{- end }}

{{/*
Generate cluster name if not provided
*/}}
{{- define "kubernetes-mcp-proxy.clusterName" -}}
{{- if .Values.proxy.clusterName }}
{{- .Values.proxy.clusterName }}
{{- else }}
{{- printf "%s/%s" .Release.Namespace .Release.Name }}
{{- end }}
{{- end }}
