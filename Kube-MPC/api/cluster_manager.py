import yaml
import json
import subprocess
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging
from datetime import datetime
import hashlib

logger = logging.getLogger(__name__)


class ClusterManager:
    """Manages multiple Kubernetes cluster configurations and connections"""

    def __init__(self, config_dir: str = "clusters"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        self.clusters: Dict[str, Dict[str, Any]] = {}
        self.active_cluster_id: Optional[str] = None
        self.load_clusters()

    def load_clusters(self):
        """Load all cluster configurations from disk"""
        try:
            clusters_file = self.config_dir / "clusters.json"
            if clusters_file.exists():
                with open(clusters_file, "r") as f:
                    self.clusters = json.load(f)
                logger.info(f"Loaded {len(self.clusters)} cluster configurations")
            else:
                # Initialize with default local cluster if kubectl is available
                self._detect_local_cluster()
        except Exception as e:
            logger.error(f"Error loading clusters: {e}")
            self.clusters = {}

    def save_clusters(self):
        """Save cluster configurations to disk"""
        try:
            clusters_file = self.config_dir / "clusters.json"
            with open(clusters_file, "w") as f:
                json.dump(self.clusters, f, indent=2, default=str)
            logger.info("Cluster configurations saved")
        except Exception as e:
            logger.error(f"Error saving clusters: {e}")

    def _detect_local_cluster(self):
        """Detect if there's a local cluster available"""
        try:
            # Check if kubectl is available and configured
            result = subprocess.run(
                ["kubectl", "cluster-info", "--request-timeout=5s"],
                capture_output=True,
                text=True,
                timeout=10,
            )

            if result.returncode == 0:
                # Get current context
                context_result = subprocess.run(
                    ["kubectl", "config", "current-context"],
                    capture_output=True,
                    text=True,
                )

                context_name = (
                    context_result.stdout.strip()
                    if context_result.returncode == 0
                    else "default"
                )

                self.clusters["local"] = {
                    "id": "local",
                    "name": "Local Cluster",
                    "context": context_name,
                    "type": "local",
                    "status": "online",
                    "created_at": datetime.now().isoformat(),
                    "kubeconfig_path": None,  # Uses default kubectl config
                }
                self.active_cluster_id = "local"
                self.save_clusters()
                logger.info("Detected local Kubernetes cluster")
        except Exception as e:
            logger.warning(f"No local cluster detected: {e}")

    def add_cluster(
        self, name: str, kubeconfig_content: str, source: str = "upload"
    ) -> str:
        """Add a new cluster configuration"""
        try:
            # Generate cluster ID
            cluster_id = self._generate_cluster_id(name, kubeconfig_content)

            # Validate kubeconfig
            cluster_info = self._validate_kubeconfig(kubeconfig_content)

            # Save kubeconfig file
            kubeconfig_path = self.config_dir / f"{cluster_id}.yaml"
            with open(kubeconfig_path, "w") as f:
                f.write(kubeconfig_content)

            # Test connectivity
            status = self._test_cluster_connectivity(str(kubeconfig_path))

            # Store cluster configuration
            self.clusters[cluster_id] = {
                "id": cluster_id,
                "name": name,
                "context": cluster_info.get("current_context", "unknown"),
                "type": "remote",
                "status": status,
                "source": source,
                "created_at": datetime.now().isoformat(),
                "kubeconfig_path": str(kubeconfig_path),
                "server": cluster_info.get("server", "unknown"),
                "cluster_name": cluster_info.get("cluster_name", "unknown"),
            }

            # Set as active if it's the first cluster
            if not self.active_cluster_id:
                self.active_cluster_id = cluster_id

            self.save_clusters()
            logger.info(f"Added cluster: {name} ({cluster_id})")
            return cluster_id

        except Exception as e:
            logger.error(f"Error adding cluster {name}: {e}")
            raise

    def _generate_cluster_id(self, name: str, kubeconfig_content: str) -> str:
        """Generate a unique cluster ID"""
        content_hash = hashlib.md5(kubeconfig_content.encode()).hexdigest()[:8]
        safe_name = "".join(c for c in name if c.isalnum() or c in "-_").lower()
        return f"{safe_name}-{content_hash}"

    def _validate_kubeconfig(self, kubeconfig_content: str) -> Dict[str, Any]:
        """Validate kubeconfig content and extract information"""
        try:
            config = yaml.safe_load(kubeconfig_content)

            if not isinstance(config, dict):
                raise ValueError("Invalid kubeconfig format")

            if "clusters" not in config or not config["clusters"]:
                raise ValueError("No clusters found in kubeconfig")

            if "users" not in config or not config["users"]:
                raise ValueError("No users found in kubeconfig")

            if "contexts" not in config or not config["contexts"]:
                raise ValueError("No contexts found in kubeconfig")

            # Extract cluster information
            current_context = config.get("current-context", "")
            cluster_info = {
                "current_context": current_context,
                "clusters": [c["name"] for c in config["clusters"]],
                "users": [u["name"] for u in config["users"]],
                "contexts": [ctx["name"] for ctx in config["contexts"]],
            }

            # Get server URL from first cluster
            if config["clusters"]:
                first_cluster = config["clusters"][0]
                if "cluster" in first_cluster and "server" in first_cluster["cluster"]:
                    cluster_info["server"] = first_cluster["cluster"]["server"]
                cluster_info["cluster_name"] = first_cluster["name"]

            return cluster_info

        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML format: {e}")
        except Exception as e:
            raise ValueError(f"Error validating kubeconfig: {e}")

    def _test_cluster_connectivity(self, kubeconfig_path: Optional[str]) -> str:
        """Test connectivity to a cluster"""
        try:
            # Build kubectl command
            if kubeconfig_path:
                cmd = [
                    "kubectl",
                    "--kubeconfig",
                    kubeconfig_path,
                    "cluster-info",
                    "--request-timeout=10s",
                ]
            else:
                # Use default kubectl config for local cluster
                cmd = ["kubectl", "cluster-info", "--request-timeout=10s"]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)

            if result.returncode == 0:
                return "online"
            else:
                logger.warning(f"Cluster connectivity test failed: {result.stderr}")
                return "offline"

        except subprocess.TimeoutExpired:
            return "timeout"
        except Exception as e:
            logger.error(f"Error testing cluster connectivity: {e}")
            return "error"

    def get_clusters(self) -> List[Dict[str, Any]]:
        """Get list of all clusters"""
        return list(self.clusters.values())

    def get_cluster(self, cluster_id: str) -> Optional[Dict[str, Any]]:
        """Get specific cluster configuration"""
        return self.clusters.get(cluster_id)

    def set_active_cluster(self, cluster_id: str) -> bool:
        """Set the active cluster"""
        if cluster_id in self.clusters:
            self.active_cluster_id = cluster_id
            self.save_clusters()
            logger.info(f"Set active cluster: {cluster_id}")
            return True
        return False

    def get_active_cluster(self) -> Optional[Dict[str, Any]]:
        """Get the currently active cluster"""
        if self.active_cluster_id:
            return self.clusters.get(self.active_cluster_id)
        return None

    def remove_cluster(self, cluster_id: str) -> bool:
        """Remove a cluster configuration"""
        try:
            if cluster_id not in self.clusters:
                return False

            cluster = self.clusters[cluster_id]

            # Remove kubeconfig file if it exists
            if cluster.get("kubeconfig_path") and cluster["type"] == "remote":
                kubeconfig_path = Path(cluster["kubeconfig_path"])
                if kubeconfig_path.exists():
                    kubeconfig_path.unlink()

            # Remove from clusters dict
            del self.clusters[cluster_id]

            # Update active cluster if needed
            if self.active_cluster_id == cluster_id:
                remaining_clusters = list(self.clusters.keys())
                self.active_cluster_id = (
                    remaining_clusters[0] if remaining_clusters else None
                )

            self.save_clusters()
            logger.info(f"Removed cluster: {cluster_id}")
            return True

        except Exception as e:
            logger.error(f"Error removing cluster {cluster_id}: {e}")
            return False

    def get_kubeconfig_path(self, cluster_id: Optional[str] = None) -> Optional[str]:
        """Get kubeconfig path for a cluster (or active cluster)"""
        cluster_id = cluster_id or self.active_cluster_id
        if not cluster_id:
            return None

        cluster = self.clusters.get(cluster_id)
        if not cluster:
            return None

        if cluster["type"] == "local":
            return None  # Use default kubectl config
        else:
            return cluster.get("kubeconfig_path")

    def refresh_cluster_status(self, cluster_id: str) -> str:
        """Refresh the status of a specific cluster"""
        cluster = self.clusters.get(cluster_id)
        if not cluster:
            return "not_found"

        if cluster["type"] == "local":
            status = self._test_cluster_connectivity(None)
        else:
            kubeconfig_path = cluster.get("kubeconfig_path")
            if not kubeconfig_path or not Path(kubeconfig_path).exists():
                status = "config_missing"
            else:
                status = self._test_cluster_connectivity(kubeconfig_path)

        # Update status
        self.clusters[cluster_id]["status"] = status
        self.clusters[cluster_id]["last_checked"] = datetime.now().isoformat()
        self.save_clusters()

        return status
