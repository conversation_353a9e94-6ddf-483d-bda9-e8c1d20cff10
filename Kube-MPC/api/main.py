from fastapi import <PERSON><PERSON><PERSON>, HTTP<PERSON>xception, WebSocket, WebSocketDisconnect, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager
from mcp_client import MC<PERSON>lient
from tunnel_server import initialize_tunnel_manager, get_tunnel_manager
from dotenv import load_dotenv
from pydantic_settings import BaseSettings
import os
import logging
import json
import secrets
from datetime import datetime

logger = logging.getLogger(__name__)

load_dotenv()


class Settings(BaseSettings):
    # Use the Kubernetes MCP server binary
    kubernetes_mcp_server_command: str = "kubernetes-mcp-server"
    kubernetes_mcp_server_args: list = []


settings = Settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    client = MCPClient()
    try:
        # Initialize tunnel manager
        tunnel_secret = os.getenv("TUNNEL_SECRET_KEY", secrets.token_urlsafe(32))
        initialize_tunnel_manager(tunnel_secret)
        logger.info("Tunnel manager initialized")

        # Get active cluster from cluster manager
        active_cluster = client.cluster_manager.get_active_cluster()
        cluster_id = active_cluster["id"] if active_cluster else None

        connected = await client.connect_to_kubernetes_server(
            settings.kubernetes_mcp_server_command,
            settings.kubernetes_mcp_server_args,
            cluster_id,
        )
        if not connected:
            raise HTTPException(
                status_code=500, detail="Failed to connect to Kubernetes MCP server"
            )
        app.state.client = client

        cluster_name = active_cluster["name"] if active_cluster else "default"
        logger.info(f"MCP client initialized successfully with cluster: {cluster_name}")

        yield
    except Exception as e:
        print(f"Error during lifespan: {e}")
        raise HTTPException(status_code=500, detail="Error during lifespan") from e
    finally:
        # shutdown
        await client.cleanup()


app = FastAPI(title="Kubernetes MCP Chat", lifespan=lifespan)


# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Mount static files
static_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static")
if os.path.exists(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")


class QueryRequest(BaseModel):
    query: str
    cluster: str = "default"  # Support for multi-cluster


class Message(BaseModel):
    role: str
    content: Any


class ToolCall(BaseModel):
    name: str
    args: Dict[str, Any]


@app.get("/")
async def read_root():
    """Serve the main chat interface"""
    static_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static")
    working_path = os.path.join(static_dir, "working-interface.html")
    if os.path.exists(working_path):
        return FileResponse(working_path)
    else:
        raise HTTPException(status_code=404, detail="Chat interface not found")


@app.post("/query")
async def process_query(request: QueryRequest):
    """Process a query and return the response"""
    try:
        if not hasattr(app.state, "client") or app.state.client is None:
            raise HTTPException(
                status_code=503,
                detail="Kubernetes MCP server is not connected. Please check your configuration and restart the server.",
            )

        messages = await app.state.client.process_query(request.query)
        return {"messages": messages}
    except HTTPException:
        raise
    except Exception as e:
        error_msg = str(e)
        if "Could not resolve authentication method" in error_msg:
            raise HTTPException(
                status_code=401,
                detail="Anthropic API key is not configured. Please set ANTHROPIC_API_KEY in your .env file.",
            )
        elif (
            "Connection refused" in error_msg
            or "No such file or directory" in error_msg
        ):
            raise HTTPException(
                status_code=503,
                detail="Failed to connect to Kubernetes MCP server. Please ensure kubectl is configured and the cluster is accessible.",
            )
        else:
            raise HTTPException(
                status_code=500, detail=f"An error occurred: {error_msg}"
            )


@app.post("/clear-conversation")
async def clear_conversation():
    """Clear the conversation history for a new chat session"""
    try:
        if not hasattr(app.state, "client") or app.state.client is None:
            raise HTTPException(
                status_code=503, detail="Kubernetes MCP server is not connected."
            )

        app.state.client.clear_conversation_history()
        return {"status": "success", "message": "Conversation history cleared"}
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to clear conversation: {str(e)}"
        )


@app.post("/clusters")
async def add_cluster(cluster_data: dict):
    """Add a new Kubernetes cluster configuration"""
    try:
        if not hasattr(app.state, "client") or app.state.client is None:
            raise HTTPException(
                status_code=503, detail="MCP client is not initialized."
            )

        name = cluster_data.get("name")
        kubeconfig = cluster_data.get("kubeconfig")
        source = cluster_data.get("source", "upload")

        if not name or not kubeconfig:
            raise HTTPException(
                status_code=400, detail="Both 'name' and 'kubeconfig' are required"
            )

        # Add cluster using cluster manager
        cluster_id = app.state.client.cluster_manager.add_cluster(
            name, kubeconfig, source
        )

        return {
            "status": "success",
            "message": f"Cluster '{name}' added successfully",
            "cluster_id": cluster_id,
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add cluster: {str(e)}")


@app.get("/clusters")
async def list_clusters():
    """List available Kubernetes clusters"""
    try:
        if not hasattr(app.state, "client") or app.state.client is None:
            raise HTTPException(
                status_code=503, detail="MCP client is not initialized."
            )

        clusters = app.state.client.cluster_manager.get_clusters()
        active_cluster = app.state.client.cluster_manager.get_active_cluster()

        return {
            "clusters": clusters,
            "active_cluster_id": active_cluster["id"] if active_cluster else None,
        }
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to list clusters: {str(e)}"
        )


@app.delete("/clusters/{cluster_id}")
async def remove_cluster(cluster_id: str):
    """Remove a Kubernetes cluster configuration"""
    try:
        if not hasattr(app.state, "client") or app.state.client is None:
            raise HTTPException(
                status_code=503, detail="MCP client is not initialized."
            )

        success = app.state.client.cluster_manager.remove_cluster(cluster_id)
        if success:
            return {
                "status": "success",
                "message": f"Cluster {cluster_id} removed successfully",
            }
        else:
            raise HTTPException(
                status_code=404, detail=f"Cluster {cluster_id} not found"
            )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to remove cluster: {str(e)}"
        )


@app.post("/clusters/{cluster_id}/activate")
async def activate_cluster(cluster_id: str):
    """Set a cluster as the active cluster"""
    try:
        if not hasattr(app.state, "client") or app.state.client is None:
            raise HTTPException(
                status_code=503, detail="MCP client is not initialized."
            )

        success = app.state.client.cluster_manager.set_active_cluster(cluster_id)
        if success:
            # For now, just set the active cluster without reconnecting
            # The next query will use the new cluster context
            cluster = app.state.client.cluster_manager.get_cluster(cluster_id)
            cluster_name = cluster["name"] if cluster else cluster_id

            return {
                "status": "success",
                "message": f"Cluster '{cluster_name}' activated. Next operations will use this cluster.",
            }
        else:
            raise HTTPException(
                status_code=404, detail=f"Cluster {cluster_id} not found"
            )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to activate cluster: {str(e)}"
        )


@app.post("/clusters/{cluster_id}/refresh")
async def refresh_cluster_status(cluster_id: str):
    """Refresh the connectivity status of a cluster"""
    try:
        if not hasattr(app.state, "client") or app.state.client is None:
            raise HTTPException(
                status_code=503, detail="MCP client is not initialized."
            )

        status = app.state.client.cluster_manager.refresh_cluster_status(cluster_id)
        return {"status": "success", "cluster_status": status}
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to refresh cluster status: {str(e)}"
        )


@app.get("/tools")
async def get_tools():
    """Get the list of available tools"""
    try:
        if not hasattr(app.state, "client") or app.state.client is None:
            raise HTTPException(
                status_code=503,
                detail="Kubernetes MCP server is not connected. Please check your configuration and restart the server.",
            )

        tools = await app.state.client.get_mcp_tools()
        return {
            "tools": [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.inputSchema,
                }
                for tool in tools
            ]
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve tools: {str(e)}"
        )


# ============================================================================
# TUNNEL ENDPOINTS FOR SAAS FUNCTIONALITY
# ============================================================================


class TunnelClusterRequest(BaseModel):
    cluster_name: str
    description: Optional[str] = None


@app.post("/tunnel/register")
async def register_tunnel_cluster(request: TunnelClusterRequest):
    """Register a new cluster for tunnel-based connection"""
    try:
        tunnel_manager = get_tunnel_manager()

        # Generate unique cluster ID
        cluster_id = f"tunnel-{secrets.token_urlsafe(8)}"

        # Generate authentication token
        auth_token = tunnel_manager.generate_cluster_token(
            cluster_id, request.cluster_name
        )

        return {
            "status": "success",
            "cluster_id": cluster_id,
            "cluster_name": request.cluster_name,
            "auth_token": auth_token,
            "tunnel_url": f"wss://{os.getenv('TUNNEL_HOST', 'localhost:8000')}/tunnel/{cluster_id}",
            "instructions": {
                "helm_install": f"helm install k8s-saas-proxy ./k8s-proxy-chart --set clusterID={cluster_id} --set authToken={auth_token} --set tunnelURL=wss://{os.getenv('TUNNEL_HOST', 'localhost:8000')}/tunnel/{cluster_id}"
            },
        }
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to register cluster: {str(e)}"
        )


@app.websocket("/tunnel/{cluster_id}")
async def tunnel_websocket(websocket: WebSocket, cluster_id: str):
    """WebSocket endpoint for tunnel connections from customer clusters"""
    await websocket.accept()

    try:
        tunnel_manager = get_tunnel_manager()

        # Get authentication token from query params or headers
        auth_token = websocket.query_params.get("token")
        if not auth_token:
            await websocket.close(code=4001, reason="Missing authentication token")
            return

        # Connect the cluster tunnel
        connected = await tunnel_manager.connect_cluster(
            cluster_id, websocket, auth_token
        )
        if not connected:
            await websocket.close(code=4003, reason="Authentication failed")
            return

        logger.info(f"Tunnel connected for cluster: {cluster_id}")

        # Handle incoming messages
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)

                message_type = message.get("type")

                if message_type == "pong":
                    # Handle pong response
                    session = tunnel_manager.get_session(cluster_id)
                    if session:
                        session.last_ping = datetime.now()

                elif message_type == "k8s_response":
                    # Handle Kubernetes API response
                    session = tunnel_manager.get_session(cluster_id)
                    if session:
                        await session.handle_response(message)

                else:
                    logger.warning(f"Unknown message type: {message_type}")

            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                logger.error("Invalid JSON received from tunnel")
            except Exception as e:
                logger.error(f"Error handling tunnel message: {e}")
                break

    except Exception as e:
        logger.error(f"Error in tunnel websocket: {e}")

    finally:
        # Clean up tunnel session
        try:
            tunnel_manager = get_tunnel_manager()
            await tunnel_manager.disconnect_cluster(cluster_id)
        except Exception:
            pass


@app.get("/tunnel/status")
async def get_tunnel_status():
    """Get status of all tunnel connections"""
    try:
        tunnel_manager = get_tunnel_manager()
        status = tunnel_manager.get_cluster_status()

        return {
            "status": "success",
            "connected_clusters": len(status),
            "clusters": status,
        }
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get tunnel status: {str(e)}"
        )


@app.post("/tunnel/{cluster_id}/proxy")
async def proxy_kubernetes_request(cluster_id: str, request: Request):
    """Proxy Kubernetes API requests through tunnel"""
    try:
        tunnel_manager = get_tunnel_manager()

        # Get request details
        method = request.method
        path = str(request.url.path).replace(f"/tunnel/{cluster_id}/proxy", "")
        headers = dict(request.headers)
        body = await request.body()

        # Remove proxy-specific headers
        headers.pop("host", None)
        headers.pop("content-length", None)

        # Proxy the request
        response = await tunnel_manager.proxy_kubernetes_request(
            cluster_id, method, path, headers, body if body else None
        )

        return JSONResponse(
            content=response["body"].decode() if response["body"] else "",
            status_code=response["status_code"],
            headers=response["headers"],
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error proxying request: {e}")
        raise HTTPException(status_code=500, detail=f"Proxy error: {str(e)}")


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
