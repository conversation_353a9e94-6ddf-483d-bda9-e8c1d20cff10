# File generated from our OpenAPI spec by <PERSON><PERSON>less. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Union
from typing_extensions import TypeAlias

from .text_block_param import Text<PERSON>lockParam
from .image_block_param import <PERSON><PERSON><PERSON>Param
from .document_block_param import <PERSON>ume<PERSON><PERSON><PERSON><PERSON>aram
from .thinking_block_param import Thinking<PERSON><PERSON><PERSON>ara<PERSON>
from .tool_use_block_param import ToolU<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .tool_result_block_param import ToolR<PERSON>ult<PERSON><PERSON>Param
from .server_tool_use_block_param import Server<PERSON>ool<PERSON><PERSON><PERSON><PERSON>Param
from .redacted_thinking_block_param import RedactedThinking<PERSON><PERSON>Param
from .web_search_tool_result_block_param import WebSearchToolResultBlockParam

__all__ = ["ContentBlockParam"]

ContentBlockParam: TypeAlias = Union[
    TextBlockParam,
    ImageBlockParam,
    DocumentBlockParam,
    ThinkingBlockParam,
    RedactedThinkingBlockParam,
    ToolUse<PERSON>lockParam,
    ToolResultBlockParam,
    ServerToolUse<PERSON>lockPara<PERSON>,
    WebSearchToolResultBlockParam,
]
