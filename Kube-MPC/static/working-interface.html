<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kubernetes MCP Client</title>
    <!-- Markdown and syntax highlighting libraries -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #0f0f23;
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: #1a1a2e;
            border-right: 1px solid #2a2a3e;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #2a2a3e;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .clusters-section {
            flex: 1;
            padding: 20px;
        }

        .section-title {
            font-size: 14px;
            font-weight: 500;
            color: #8b8b8b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .section-title::before {
            content: "🔗";
            margin-right: 8px;
        }

        .upload-area {
            border: 2px dashed #4a4a5e;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #007bff;
            background: rgba(0, 123, 255, 0.1);
        }

        .upload-area.dragover {
            border-color: #007bff;
            background: rgba(0, 123, 255, 0.2);
        }

        .upload-text {
            font-size: 14px;
            color: #8b8b8b;
        }

        .cluster-item {
            background: #2a2a3e;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .cluster-item:hover {
            background: #3a3a4e;
        }

        .cluster-item.active {
            background: #007bff;
        }

        .cluster-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            margin-bottom: 8px;
        }

        .cluster-name {
            font-weight: 500;
            font-size: 14px;
        }

        .cluster-actions {
            display: flex;
            gap: 4px;
        }

        .cluster-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 4px;
            padding: 4px 6px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s ease;
        }

        .cluster-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .cluster-details {
            font-size: 11px;
            color: #8b8b8b;
            line-height: 1.4;
        }

        .cluster-status {
            font-weight: 500;
            margin-bottom: 2px;
        }

        .cluster-context, .cluster-server {
            margin-bottom: 1px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .sessions-section {
            padding: 20px;
            border-top: 1px solid #2a2a3e;
        }

        .clear-chat-btn {
            width: 100%;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .clear-chat-btn:hover {
            background: #c82333;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background: #1a1a2e;
            padding: 20px;
            border-bottom: 1px solid #2a2a3e;
        }

        .chat-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .chat-subtitle {
            color: #8b8b8b;
            font-size: 14px;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .message {
            margin-bottom: 20px;
            max-width: 80%;
        }

        .message.user {
            margin-left: auto;
        }

        .message-content {
            background: #2a2a3e;
            padding: 15px;
            border-radius: 12px;
            word-wrap: break-word;
            line-height: 1.6;
        }

        .message.user .message-content {
            background: #007bff;
            white-space: pre-wrap;
        }

        /* Markdown styling */
        .message-content h1,
        .message-content h2,
        .message-content h3,
        .message-content h4,
        .message-content h5,
        .message-content h6 {
            color: #ffffff;
            margin: 16px 0 8px 0;
            font-weight: 600;
        }

        .message-content h1 { font-size: 1.5em; }
        .message-content h2 { font-size: 1.3em; }
        .message-content h3 { font-size: 1.2em; }
        .message-content h4 { font-size: 1.1em; }

        .message-content p {
            margin: 8px 0;
        }

        .message-content ul,
        .message-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .message-content li {
            margin: 4px 0;
        }

        .message-content blockquote {
            border-left: 4px solid #007bff;
            margin: 16px 0;
            padding: 8px 16px;
            background: rgba(0, 123, 255, 0.1);
            border-radius: 4px;
        }

        .message-content code {
            background: #1a1a2e;
            color: #e6e6e6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
        }

        .message-content pre {
            background: #1a1a2e;
            border: 1px solid #4a4a5e;
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
            overflow-x: auto;
            position: relative;
        }

        .message-content pre code {
            background: none;
            padding: 0;
            border-radius: 0;
            font-size: 0.85em;
            line-height: 1.4;
        }

        /* Code block header */
        .code-header {
            background: #1a1a2e;
            border: 1px solid #4a4a5e;
            border-bottom: none;
            border-radius: 8px 8px 0 0;
            padding: 8px 16px;
            font-size: 12px;
            color: #8b8b8b;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .code-header + pre {
            border-radius: 0 0 8px 8px;
            margin-top: 0;
        }

        .copy-btn {
            background: #4a4a5e;
            border: none;
            color: #ffffff;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .copy-btn:hover {
            background: #007bff;
        }

        /* Table styling */
        .message-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 12px 0;
            font-size: 0.9em;
        }

        .message-content th,
        .message-content td {
            border: 1px solid #4a4a5e;
            padding: 8px 12px;
            text-align: left;
        }

        .message-content th {
            background: #1a1a2e;
            font-weight: 600;
        }

        .message-content tr:nth-child(even) {
            background: rgba(255, 255, 255, 0.05);
        }

        /* Links */
        .message-content a {
            color: #007bff;
            text-decoration: none;
        }

        .message-content a:hover {
            text-decoration: underline;
        }

        /* Strong and emphasis */
        .message-content strong {
            font-weight: 600;
            color: #ffffff;
        }

        .message-content em {
            font-style: italic;
            color: #e6e6e6;
        }

        .message-role {
            font-size: 12px;
            color: #8b8b8b;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .input-container {
            background: #1a1a2e;
            padding: 20px;
            border-top: 1px solid #2a2a3e;
        }

        .input-wrapper {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            background: #2a2a3e;
            border: 1px solid #4a4a5e;
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 14px;
            resize: vertical;
            min-height: 44px;
            max-height: 120px;
        }

        .message-input:focus {
            outline: none;
            border-color: #007bff;
        }

        .send-btn {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.3s ease;
            height: 44px;
        }

        .send-btn:hover:not(:disabled) {
            background: #0056b3;
        }

        .send-btn:disabled {
            background: #4a4a5e;
            cursor: not-allowed;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: #28a745;
        }

        .notification.error {
            background: #dc3545;
        }

        .notification.info {
            background: #17a2b8;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #4a4a5e;
            border-radius: 50%;
            border-top-color: #007bff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: #8b8b8b;
        }

        .welcome-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #ffffff;
        }

        .welcome-subtitle {
            font-size: 16px;
            margin-bottom: 30px;
        }

        .example-queries {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            max-width: 600px;
            margin: 0 auto;
        }

        .example-query {
            background: #2a2a3e;
            border: 1px solid #4a4a5e;
            border-radius: 20px;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .example-query:hover {
            background: #007bff;
            border-color: #007bff;
        }

        .hidden {
            display: none;
        }

        input[type="file"] {
            display: none;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">Kubernetes MCP Client</div>
            </div>
            
            <div class="clusters-section">
                <div class="section-title">Clusters</div>
                
                <div class="upload-area" id="uploadArea">
                    <div class="upload-text">
                        📁 Drop kubeconfig file here<br>
                        <small>or click to browse</small>
                    </div>
                    <input type="file" id="fileInput" accept=".yaml,.yml,.config">
                </div>
                
                <div id="clustersList">
                    <!-- Clusters will be loaded here -->
                </div>
            </div>
            
            <div class="sessions-section">
                <div class="section-title">Sessions</div>
                <button class="clear-chat-btn" id="clearChatBtn">Clear Chat</button>
            </div>
        </div>
        
        <div class="main-content">
            <div class="chat-header">
                <div class="chat-title">🤖 Kubernetes Assistant</div>
                <div class="chat-subtitle">Hello! I'm your Kubernetes assistant. I can help you manage your cluster, list pods, check deployments, and much more.</div>
            </div>
            
            <div class="chat-container" id="chatContainer">
                <div class="welcome-message" id="welcomeMessage">
                    <div class="welcome-title">Welcome to Kubernetes MCP Client</div>
                    <div class="welcome-subtitle">Try asking me something like:</div>
                    <div class="example-queries">
                        <div class="example-query" onclick="sendExampleQuery('list all pods')">📦 "list all pods"</div>
                        <div class="example-query" onclick="sendExampleQuery('show me deployments in default namespace')">🚀 "show deployments in default namespace"</div>
                        <div class="example-query" onclick="sendExampleQuery('get pod logs for nginx')">📋 "get pod logs for nginx"</div>
                        <div class="example-query" onclick="sendExampleQuery('create a test pod')">➕ "create a test pod"</div>
                    </div>
                </div>
            </div>
            
            <div class="input-container">
                <div class="input-wrapper">
                    <textarea 
                        class="message-input" 
                        id="messageInput" 
                        placeholder="Ask me about your Kubernetes cluster..."
                        rows="1"
                    ></textarea>
                    <button class="send-btn" id="sendBtn">Send</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global state
        let currentCluster = null;
        let isLoading = false;

        // DOM elements
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const clustersList = document.getElementById('clustersList');
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const clearChatBtn = document.getElementById('clearChatBtn');
        const welcomeMessage = document.getElementById('welcomeMessage');

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Kubernetes MCP Client initialized');

            // Configure Prism.js
            if (typeof Prism !== 'undefined') {
                Prism.plugins.autoloader.languages_path = 'https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/';

                // Ensure common languages are loaded
                Prism.plugins.autoloader.loadLanguages(['yaml', 'json', 'bash', 'shell', 'dockerfile', 'javascript', 'python']);
            }

            loadClusters();
            setupEventListeners();
        });

        function setupEventListeners() {
            // File upload
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('drop', handleDrop);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            fileInput.addEventListener('change', handleFileSelect);

            // Message sending
            sendBtn.addEventListener('click', sendMessage);
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Auto-resize textarea
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            // Clear chat
            clearChatBtn.addEventListener('click', clearChat);
        }

        // File upload handlers
        function handleDragOver(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFileUpload(file);
            }
        }

        async function handleFileUpload(file) {
            if (!file.name.match(/\.(yaml|yml|config)$/i)) {
                showNotification('Please select a valid kubeconfig file (.yaml, .yml, or .config)', 'error');
                return;
            }

            try {
                showNotification('Uploading kubeconfig...', 'info');
                
                const content = await readFileAsText(file);
                let clusterName = file.name.replace(/\.(yaml|yml|config)$/i, '');
                
                if (clusterName === 'kubeconfig' || clusterName === 'config') {
                    clusterName = 'cluster-' + Date.now();
                }

                const clusterData = {
                    name: clusterName,
                    kubeconfig: content,
                    source: 'file-upload'
                };

                const response = await fetch('/clusters', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(clusterData)
                });

                if (response.ok) {
                    showNotification(`Cluster "${clusterName}" added successfully!`, 'success');
                    loadClusters();
                } else {
                    const error = await response.text();
                    showNotification(`Failed to add cluster: ${error}`, 'error');
                }
            } catch (error) {
                console.error('Error uploading kubeconfig:', error);
                showNotification(`Error uploading kubeconfig: ${error.message}`, 'error');
            }
        }

        function readFileAsText(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = e => resolve(e.target.result);
                reader.onerror = reject;
                reader.readAsText(file);
            });
        }

        // Cluster management
        async function loadClusters() {
            try {
                const response = await fetch('/clusters');
                const data = await response.json();

                clustersList.innerHTML = '';

                if (data.clusters && data.clusters.length > 0) {
                    data.clusters.forEach(cluster => {
                        const clusterElement = createClusterElement(cluster);

                        // Mark active cluster
                        if (data.active_cluster_id && cluster.id === data.active_cluster_id) {
                            clusterElement.classList.add('active');
                            currentCluster = cluster;
                        }

                        clustersList.appendChild(clusterElement);
                    });
                } else {
                    clustersList.innerHTML = '<div style="color: #8b8b8b; text-align: center; padding: 20px;">No clusters configured<br><small>Upload a kubeconfig file to get started</small></div>';
                }
            } catch (error) {
                console.error('Error loading clusters:', error);
                showNotification('Failed to load clusters', 'error');
            }
        }

        function createClusterElement(cluster) {
            const div = document.createElement('div');
            div.className = 'cluster-item';

            // Status indicator
            const statusColor = cluster.status === 'online' ? '#28a745' :
                               cluster.status === 'offline' ? '#dc3545' : '#ffc107';

            div.innerHTML = `
                <div class="cluster-header" onclick="selectCluster('${cluster.id}')">
                    <div class="cluster-name">${cluster.name}</div>
                    <div class="cluster-actions">
                        <button class="cluster-btn refresh-btn" onclick="refreshCluster('${cluster.id}')" title="Refresh Status">🔄</button>
                        ${cluster.type !== 'local' ? `<button class="cluster-btn delete-btn" onclick="deleteCluster('${cluster.id}')" title="Delete Cluster">🗑️</button>` : ''}
                    </div>
                </div>
                <div class="cluster-details">
                    <div class="cluster-status" style="color: ${statusColor}">● ${cluster.status}</div>
                    <div class="cluster-context">${cluster.context || 'Unknown context'}</div>
                    ${cluster.server ? `<div class="cluster-server">${cluster.server}</div>` : ''}
                </div>
            `;

            return div;
        }

        async function selectCluster(clusterId) {
            try {
                // Find cluster by ID
                const response = await fetch('/clusters');
                const data = await response.json();
                const cluster = data.clusters.find(c => c.id === clusterId);

                if (!cluster) {
                    showNotification('Cluster not found', 'error');
                    return;
                }

                showNotification(`Activating cluster: ${cluster.name}...`, 'info');

                // Activate cluster on backend
                const activateResponse = await fetch(`/clusters/${cluster.id}/activate`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (activateResponse.ok) {
                    currentCluster = cluster;

                    // Update UI
                    document.querySelectorAll('.cluster-item').forEach(item => {
                        item.classList.remove('active');
                    });

                    // Find and activate the correct cluster item
                    const clusterElements = document.querySelectorAll('.cluster-item');
                    clusterElements.forEach(element => {
                        if (element.innerHTML.includes(cluster.name)) {
                            element.classList.add('active');
                        }
                    });

                    showNotification(`Cluster activated: ${cluster.name}`, 'success');
                } else {
                    const error = await activateResponse.text();
                    showNotification(`Failed to activate cluster: ${error}`, 'error');
                }
            } catch (error) {
                console.error('Error activating cluster:', error);
                showNotification(`Error activating cluster: ${error.message}`, 'error');
            }
        }

        async function refreshCluster(clusterId) {
            try {
                showNotification('Refreshing cluster status...', 'info');

                const response = await fetch(`/clusters/${clusterId}/refresh`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (response.ok) {
                    const result = await response.json();
                    showNotification(`Cluster status refreshed: ${result.cluster_status}`, 'success');
                    loadClusters(); // Reload cluster list to show updated status
                } else {
                    const error = await response.text();
                    showNotification(`Failed to refresh cluster: ${error}`, 'error');
                }
            } catch (error) {
                console.error('Error refreshing cluster:', error);
                showNotification(`Error refreshing cluster: ${error.message}`, 'error');
            }
        }

        async function deleteCluster(clusterId) {
            try {
                if (!confirm('Are you sure you want to delete this cluster configuration?')) {
                    return;
                }

                showNotification('Deleting cluster...', 'info');

                const response = await fetch(`/clusters/${clusterId}`, {
                    method: 'DELETE',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (response.ok) {
                    showNotification('Cluster deleted successfully', 'success');
                    loadClusters(); // Reload cluster list
                } else {
                    const error = await response.text();
                    showNotification(`Failed to delete cluster: ${error}`, 'error');
                }
            } catch (error) {
                console.error('Error deleting cluster:', error);
                showNotification(`Error deleting cluster: ${error.message}`, 'error');
            }
        }

        // Message handling
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isLoading) return;

            try {
                isLoading = true;
                sendBtn.disabled = true;
                sendBtn.innerHTML = '<div class="loading"></div>';

                // Hide welcome message
                welcomeMessage.classList.add('hidden');

                // Add user message to chat
                addMessage(message, 'user');

                // Clear input
                messageInput.value = '';
                messageInput.style.height = 'auto';

                // Send to API
                const response = await fetch('/query', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        query: message,
                        cluster_id: currentCluster?.id
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                // Handle response
                let responseContent = 'No response received';
                if (data.messages && Array.isArray(data.messages) && data.messages.length > 0) {
                    const lastMessage = data.messages[data.messages.length - 1];
                    if (lastMessage && lastMessage.content && Array.isArray(lastMessage.content)) {
                        responseContent = lastMessage.content.map(c => c.text || c).join('\n');
                    } else if (lastMessage && lastMessage.content) {
                        responseContent = lastMessage.content;
                    } else if (typeof lastMessage === 'string') {
                        responseContent = lastMessage;
                    }
                }

                // Add assistant response to chat
                addMessage(responseContent, 'assistant');

            } catch (error) {
                console.error('Error sending message:', error);
                addMessage(`Error: ${error.message}`, 'assistant');
                showNotification('Failed to send message', 'error');
            } finally {
                isLoading = false;
                sendBtn.disabled = false;
                sendBtn.innerHTML = 'Send';
            }
        }

        function sendExampleQuery(query) {
            messageInput.value = query;
            sendMessage();
        }

        function addMessage(content, role) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            if (role === 'user') {
                // User messages are plain text
                messageContent.textContent = content;
            } else {
                // Assistant messages are rendered as markdown
                messageContent.innerHTML = renderMarkdown(content);
            }

            messageDiv.innerHTML = `
                <div class="message-role">${role === 'user' ? 'You' : 'Assistant'}</div>
            `;
            messageDiv.appendChild(messageContent);

            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;

            // Apply syntax highlighting to code blocks
            if (role === 'assistant') {
                Prism.highlightAllUnder(messageDiv);
            }
        }

        function renderMarkdown(content) {
            // Configure marked options
            marked.setOptions({
                highlight: function(code, lang) {
                    if (lang && Prism.languages[lang]) {
                        return Prism.highlight(code, Prism.languages[lang], lang);
                    }
                    return code;
                },
                breaks: true,
                gfm: true
            });

            // Custom renderer for code blocks with headers
            const renderer = new marked.Renderer();

            renderer.code = function(code, language) {
                const validLang = language && Prism.languages[language] ? language : 'text';
                const highlightedCode = validLang !== 'text' && Prism.languages[validLang]
                    ? Prism.highlight(code, Prism.languages[validLang], validLang)
                    : escapeHtml(code);

                const langDisplay = language || 'text';
                const codeId = 'code-' + Math.random().toString(36).substr(2, 9);

                return `
                    <div class="code-header">
                        <span>${langDisplay}</span>
                        <button class="copy-btn" onclick="copyCode('${codeId}')">Copy</button>
                    </div>
                    <pre><code id="${codeId}" class="language-${validLang}">${highlightedCode}</code></pre>
                `;
            };

            marked.use({ renderer });

            return marked.parse(content);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function copyCode(codeId) {
            const codeElement = document.getElementById(codeId);
            if (codeElement) {
                const text = codeElement.textContent;
                navigator.clipboard.writeText(text).then(() => {
                    showNotification('Code copied to clipboard!', 'success');
                }).catch(() => {
                    showNotification('Failed to copy code', 'error');
                });
            }
        }

        async function clearChat() {
            try {
                const response = await fetch('/clear-conversation', { method: 'POST' });
                if (response.ok) {
                    chatContainer.innerHTML = '';
                    chatContainer.appendChild(welcomeMessage);
                    welcomeMessage.classList.remove('hidden');
                    showNotification('Chat cleared', 'success');
                } else {
                    showNotification('Failed to clear chat', 'error');
                }
            } catch (error) {
                console.error('Error clearing chat:', error);
                showNotification('Failed to clear chat', 'error');
            }
        }

        // Notification system
        function showNotification(message, type = 'info') {
            // Remove existing notifications
            document.querySelectorAll('.notification').forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => notification.classList.add('show'), 100);

            // Hide notification after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
    </script>
</body>
</html>
