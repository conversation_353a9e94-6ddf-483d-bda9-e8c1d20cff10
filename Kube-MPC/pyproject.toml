[project]
name = "mcp-client-python"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "anthropic>=0.50.0",
    "beautifulsoup4>=4.13.4",
    "fastapi>=0.115.12",
    "kubernetes-mcp-server>=0.0.46",
    "mcp>=1.6.0",
    "python-dotenv>=1.1.0",
    "pyyaml>=6.0.2",
    "requests>=2.32.4",
]

[tool.mypy]
ignore_missing_imports = true
