# Default values for kubernetes-mcp-proxy
# This is a YAML-formatted file.

# Proxy configuration
proxy:
  # SaaS tunnel server URL (customers will need to set this)
  saasUrl: "wss://your-saas-domain.com/tunnel"
  
  # Cluster identification (auto-generated if not provided)
  clusterId: ""
  clusterName: ""
  
  # Connection settings
  reconnectInterval: 30
  pingInterval: 30
  
  # Image configuration
  image:
    repository: your-registry/kubernetes-mcp-proxy
    tag: "latest"
    pullPolicy: IfNotPresent
  
  # Resource limits
  resources:
    limits:
      cpu: 200m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi
  
  # Security context
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 1000
    allowPrivilegeEscalation: false
    capabilities:
      drop:
        - ALL
    readOnlyRootFilesystem: true

# Service account configuration
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  name: ""

# RBAC configuration
rbac:
  # Specifies whether RBAC resources should be created
  create: true
  # Cluster-wide permissions (needed for full kubectl access)
  clusterWide: true

# Pod configuration
podAnnotations: {}
podSecurityContext:
  fsGroup: 1000

# Node selection
nodeSelector: {}
tolerations: []
affinity: {}

# Monitoring
monitoring:
  enabled: false
  serviceMonitor:
    enabled: false
    interval: 30s
    path: /metrics

# Network policy
networkPolicy:
  enabled: false
  egress:
    # Allow outbound connections to SaaS
    - to: []
      ports:
        - protocol: TCP
          port: 443
        - protocol: TCP
          port: 80
