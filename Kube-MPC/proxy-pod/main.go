package main

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/gorilla/websocket"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

type Config struct {
	TunnelURL    string
	ClusterID    string
	AuthToken    string
	KubeConfig   string
	PingInterval time.Duration
}

type ProxyServer struct {
	config     *Config
	conn       *websocket.Conn
	kubeClient *http.Client
	kubeConfig *rest.Config
	requests   map[string]chan *TunnelResponse
}

type TunnelMessage struct {
	Type      string            `json:"type"`
	RequestID string            `json:"request_id,omitempty"`
	Method    string            `json:"method,omitempty"`
	Path      string            `json:"path,omitempty"`
	Headers   map[string]string `json:"headers,omitempty"`
	Body      string            `json:"body,omitempty"`
	Timestamp string            `json:"timestamp,omitempty"`
}

type TunnelResponse struct {
	Type       string            `json:"type"`
	RequestID  string            `json:"request_id"`
	StatusCode int               `json:"status_code"`
	Headers    map[string]string `json:"headers"`
	Body       string            `json:"body"`
}

func main() {
	config := &Config{
		TunnelURL:    getEnv("TUNNEL_URL", "ws://localhost:8000/tunnel/test"),
		ClusterID:    getEnv("CLUSTER_ID", "test-cluster"),
		AuthToken:    getEnv("AUTH_TOKEN", ""),
		KubeConfig:   getEnv("KUBECONFIG", ""),
		PingInterval: 30 * time.Second,
	}

	if config.AuthToken == "" {
		log.Fatal("AUTH_TOKEN environment variable is required")
	}

	proxy := &ProxyServer{
		config:   config,
		requests: make(map[string]chan *TunnelResponse),
	}

	// Initialize Kubernetes client
	if err := proxy.initKubeClient(); err != nil {
		log.Fatalf("Failed to initialize Kubernetes client: %v", err)
	}

	// Connect to tunnel server
	if err := proxy.connect(); err != nil {
		log.Fatalf("Failed to connect to tunnel server: %v", err)
	}

	// Handle graceful shutdown
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		log.Println("Shutting down proxy server...")
		proxy.disconnect()
		os.Exit(0)
	}()

	// Start message handling
	proxy.handleMessages()
}

func (p *ProxyServer) initKubeClient() error {
	var config *rest.Config
	var err error

	if p.config.KubeConfig != "" {
		// Use provided kubeconfig
		config, err = clientcmd.BuildConfigFromFlags("", p.config.KubeConfig)
	} else {
		// Use in-cluster config
		config, err = rest.InClusterConfig()
	}

	if err != nil {
		return fmt.Errorf("failed to create Kubernetes config: %w", err)
	}

	p.kubeConfig = config

	// Create HTTP client with proper TLS configuration
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: config.Insecure,
		},
	}

	if config.CAFile != "" || len(config.CAData) > 0 {
		// Configure CA certificate
		transport.TLSClientConfig.InsecureSkipVerify = false
	}

	p.kubeClient = &http.Client{
		Transport: transport,
		Timeout:   30 * time.Second,
	}

	log.Printf("Kubernetes client initialized for host: %s", config.Host)
	return nil
}

func (p *ProxyServer) connect() error {
	// Parse tunnel URL and add authentication token
	u, err := url.Parse(p.config.TunnelURL)
	if err != nil {
		return fmt.Errorf("invalid tunnel URL: %w", err)
	}

	// Add auth token as query parameter
	q := u.Query()
	q.Set("token", p.config.AuthToken)
	u.RawQuery = q.Encode()

	// Connect to WebSocket
	dialer := websocket.DefaultDialer
	dialer.TLSClientConfig = &tls.Config{InsecureSkipVerify: true} // For development

	conn, _, err := dialer.Dial(u.String(), nil)
	if err != nil {
		return fmt.Errorf("failed to connect to tunnel server: %w", err)
	}

	p.conn = conn
	log.Printf("Connected to tunnel server: %s", p.config.TunnelURL)

	// Start ping routine
	go p.pingRoutine()

	return nil
}

func (p *ProxyServer) disconnect() {
	if p.conn != nil {
		p.conn.Close()
	}
}

func (p *ProxyServer) pingRoutine() {
	ticker := time.NewTicker(p.config.PingInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			message := TunnelMessage{
				Type:      "ping",
				Timestamp: time.Now().Format(time.RFC3339),
			}

			if err := p.conn.WriteJSON(message); err != nil {
				log.Printf("Failed to send ping: %v", err)
				return
			}
		}
	}
}

func (p *ProxyServer) handleMessages() {
	for {
		var message TunnelMessage
		if err := p.conn.ReadJSON(&message); err != nil {
			log.Printf("Error reading message: %v", err)
			break
		}

		switch message.Type {
		case "k8s_request":
			go p.handleKubernetesRequest(&message)
		case "ping":
			// Respond with pong
			pong := TunnelMessage{
				Type:      "pong",
				Timestamp: time.Now().Format(time.RFC3339),
			}
			p.conn.WriteJSON(pong)
		default:
			log.Printf("Unknown message type: %s", message.Type)
		}
	}
}

func (p *ProxyServer) handleKubernetesRequest(message *TunnelMessage) {
	// Build Kubernetes API URL
	apiURL := strings.TrimSuffix(p.kubeConfig.Host, "/") + message.Path

	// Decode body if present
	var body io.Reader
	if message.Body != "" {
		bodyBytes, err := base64.StdEncoding.DecodeString(message.Body)
		if err != nil {
			p.sendErrorResponse(message.RequestID, 400, fmt.Sprintf("Invalid body encoding: %v", err))
			return
		}
		body = bytes.NewReader(bodyBytes)
	}

	// Create HTTP request
	req, err := http.NewRequest(message.Method, apiURL, body)
	if err != nil {
		p.sendErrorResponse(message.RequestID, 500, fmt.Sprintf("Failed to create request: %v", err))
		return
	}

	// Add headers
	for key, value := range message.Headers {
		req.Header.Set(key, value)
	}

	// Add Kubernetes authentication
	if p.kubeConfig.BearerToken != "" {
		req.Header.Set("Authorization", "Bearer "+p.kubeConfig.BearerToken)
	}

	// Execute request
	resp, err := p.kubeClient.Do(req)
	if err != nil {
		p.sendErrorResponse(message.RequestID, 500, fmt.Sprintf("Request failed: %v", err))
		return
	}
	defer resp.Body.Close()

	// Read response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		p.sendErrorResponse(message.RequestID, 500, fmt.Sprintf("Failed to read response: %v", err))
		return
	}

	// Prepare response headers
	headers := make(map[string]string)
	for key, values := range resp.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	// Send response back through tunnel
	response := TunnelResponse{
		Type:       "k8s_response",
		RequestID:  message.RequestID,
		StatusCode: resp.StatusCode,
		Headers:    headers,
		Body:       base64.StdEncoding.EncodeToString(respBody),
	}

	if err := p.conn.WriteJSON(response); err != nil {
		log.Printf("Failed to send response: %v", err)
	}

	log.Printf("Proxied %s %s -> %d", message.Method, message.Path, resp.StatusCode)
}

func (p *ProxyServer) sendErrorResponse(requestID string, statusCode int, message string) {
	response := TunnelResponse{
		Type:       "k8s_response",
		RequestID:  requestID,
		StatusCode: statusCode,
		Headers:    map[string]string{"Content-Type": "application/json"},
		Body:       base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf(`{"error": "%s"}`, message))),
	}

	p.conn.WriteJSON(response)
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
