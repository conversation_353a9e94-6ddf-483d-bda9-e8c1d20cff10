# 🚀 Kubernetes MCP Chat Assistant

A sophisticated ChatGPT-like web application that acts as an MCP (Model Context Protocol) client for managing Kubernetes clusters through natural conversation. This project demonstrates how to build a complete MCP client using Python, FastAPI, and modern web technologies.

## 🎯 What This Project Demonstrates

- **Complete MCP Implementation**: Full MCP client with conversation memory
- **AI-Powered Kubernetes Management**: Natural language interface for kubectl operations
- **Modern Web Architecture**: FastAPI backend with responsive frontend
- **Conversation Context**: Persistent memory across chat interactions
- **Multi-Cluster Support**: Manage multiple Kubernetes clusters from one interface

## ✨ Key Features

- 🎨 **ChatGPT-like Interface**: Modern, responsive web UI with dark/light themes
- 🧠 **Conversation Memory**: Maintains context for natural follow-up questions
- ⚡ **Real-time Kubernetes Operations**: Direct cluster interaction via MCP tools
- 🔧 **18 Kubernetes Tools**: Comprehensive pod, resource, and Helm management
- 🌐 **Multi-Cluster**: Drag & drop kubeconfig files for cluster switching
- 📊 **Intelligent Analysis**: AI provides insights, not just raw data

## 🏗️ Architecture Overview

```mermaid
graph LR
    A[User] --> B[Frontend]
    B --> C[FastAPI]
    C --> D[MCPClient]
    D --> E[KubeMCPServer]
    D --> F[Claude AI]
    E --> G[Kubernetes]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#e0f2f1
    style G fill:#f1f8e9
```

**Flow**: User types "list pods" → Frontend sends to FastAPI → MCP Client processes with Claude AI → Kubernetes MCP Server executes kubectl → Results formatted and returned with conversation context.

### 🔄 Workflow Steps

1. **👤 User Input**: Natural language query (e.g., "list all pods")
2. **🌐 Frontend**: Captures input, sends POST to `/query` endpoint
3. **⚡ FastAPI**: Routes request to MCP Client
4. **🧠 MCP Client**: Adds to conversation history, calls Claude AI
5. **🤖 Claude AI**: Analyzes query, selects appropriate Kubernetes tool
6. **📡 Tool Execution**: MCP Server runs kubectl command on cluster
7. **📊 Data Processing**: Raw results formatted and analyzed by AI
8. **💬 Response**: Natural language response with full conversation context
9. **🎨 Display**: Frontend renders formatted response to user

## 🎬 Live Demo

Experience the power of natural language Kubernetes management:

```
👤 "List all pods"
🤖 "I found 67 pods across 15 namespaces. Several are failing in kube-system. Investigate?"

👤 "yes"
🤖 "The issue is with Flannel networking. The /run/flannel/subnet.env file is missing..."

👤 "fix it step by step"
🤖 "I'll guide you through the Flannel repair process..."
```

## 📋 Prerequisites

- **Python 3.11+**: Required for running the application
- **Kubernetes Cluster**: Access with `kubectl` configured
- **Anthropic API Key**: For Claude AI functionality
- **Basic Knowledge**: FastAPI, REST APIs, and Kubernetes concepts

## 🚀 Quick Start

1. **Clone the Repository**:
   ```bash
   <NAME_EMAIL>:alejandro-ao/mcp-client-python.git
   cd mcp-client-python
   ```

2. **Install Dependencies**:
   ```bash
   # Install uv (modern Python package manager)
   curl -LsSf https://astral.sh/uv/install.sh | sh

   # Install Kubernetes MCP Server
   uv add kubernetes-mcp-server
   ```

3. **Configure Environment**:
   ```bash
   # Create .env file
   echo "ANTHROPIC_API_KEY=your_api_key_here" > .env

   # Ensure kubectl is configured
   kubectl cluster-info
   ```

4. **Start the Application**:
   ```bash
   cd api && uv run python main.py
   ```

5. **Open Your Browser**:
   ```
   http://localhost:8000
   ```

🎉 **You're ready!** Start chatting with your Kubernetes cluster in natural language!

## 📚 Documentation

For detailed information about the architecture, workflow, and advanced features, see:

- **[📖 Complete Documentation](README_KUBERNETES.md)** - Detailed setup, architecture diagrams, and examples
- **[🎨 New Interface Guide](README_NEW_INTERFACE.md)** - Modern UI features and design

## 🛠️ Available Tools

The application provides **18 Kubernetes tools** through the MCP interface:

| **Category** | **Tools** | **Example Commands** |
|--------------|-----------|---------------------|
| **Pods** | `pods_list`, `pods_get`, `pods_delete`, `pods_exec`, `pods_log`, `pods_run`, `pods_top` | "List all pods", "Show nginx logs", "Restart failed pods" |
| **Resources** | `resources_list`, `resources_get`, `resources_create_or_update`, `resources_delete` | "List deployments", "Create a service", "Update configmap" |
| **Helm** | `helm_list`, `helm_install`, `helm_uninstall` | "Install nginx chart", "List releases", "Remove old chart" |
| **Cluster** | `namespaces_list`, `events_list`, `configuration_view` | "Show namespaces", "Recent events", "Cluster config" |

## 🎯 What You'll Learn

This project demonstrates advanced MCP client implementation:

1. **🏗️ MCP Architecture**: Complete client-server communication via stdio
2. **🧠 Conversation Memory**: Persistent context across chat interactions
3. **🤖 AI Integration**: Claude AI for intelligent tool selection and analysis
4. **🎨 Modern Web UI**: Responsive ChatGPT-like interface with themes
5. **⚙️ Kubernetes Integration**: Real-world tool implementation for cluster management

## 🔧 Technical Stack

- **Backend**: FastAPI, Python 3.11+, MCP Protocol
- **Frontend**: Vanilla JavaScript, Modern CSS, Responsive Design
- **AI**: Anthropic Claude 3.5 Haiku
- **Tools**: Kubernetes MCP Server (18 tools)
- **Infrastructure**: Docker-ready, Multi-cluster support

## 🌟 Key Innovations

- **Conversation Context**: Unlike basic MCP examples, maintains full chat history
- **Natural Language**: AI automatically selects appropriate Kubernetes tools
- **Error Handling**: Comprehensive error management across all layers
- **Multi-Cluster**: Drag & drop kubeconfig files for cluster switching
- **Real-time Analysis**: AI provides insights, not just raw kubectl output

## 📚 Resources

- **[MCP Documentation](https://modelcontextprotocol.io/)** - Official MCP specification
- **[Kubernetes MCP Server](https://github.com/kubernetes-mcp/kubernetes-mcp-server)** - The tool server we connect to
- **[FastAPI Documentation](https://fastapi.tiangolo.com/)** - Web framework used
- **[Anthropic Claude](https://www.anthropic.com/claude)** - AI model powering the assistant

## 📄 License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Anthropic** for the Claude AI model and MCP protocol
- **Kubernetes Community** for the excellent tooling ecosystem
- **FastAPI Team** for the outstanding web framework

Special thanks to the creators of FastAPI and the Claude Desktop application for their inspiration.
